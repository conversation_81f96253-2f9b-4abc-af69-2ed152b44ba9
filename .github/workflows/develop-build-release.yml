name: Develop - Build and Release

# When this action will be executed
on:
  # Automatically trigger it when detected changes in repo
  push:
    branches: [<PERSON>elo<PERSON>]
    paths-ignore:
    - ".github/workflows/code_review.yml"    # Ignore GitHub Actions workflow changes
    - "app/src/main/python/**"                      # Ignore changes in docs directory

  # Allow mannually trigger
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      BUILD_VERSION: ${{ github.sha }}

    steps:
      - name: Checkout to the branch
        uses: actions/checkout@v2

      - name: Set up JDK 23
        uses: actions/setup-java@v2
        with:
          distribution: "zulu"
          java-version: "23"

      - name: maven-settings-xml-action
        uses: whelk-io/maven-settings-xml-action@v20
        with:
          repositories: '[{ "id": "mvn-repo", "url": "https://maven.pkg.github.com/Maersk-Global/ohRest" }]'
          servers: '[{ "id": "mvn-repo", "username": "${{ secrets.GIT_USER }}", "password": "${{ secrets.GIT_PASSWORD }}" }]'
          output_file: maven/settings.xml

      - name: Create local Maven repository
        run: mkdir -p $HOME/.m2/repository

      - name: Copy Maven settings
        run: cp maven/settings.xml $HOME/.m2

      - name: Download properties files from Azure Storage
        uses: azure/CLI@v1
        with:
          inlineScript: |
            for file in application.properties application.properties.bak applicationADM.properties applicationADM.properties.bak applicationSDE.properties applicationSDE.properties.bak applicationSDE_dev.properties applicationSDE_prod.properties applicationSDE_uat.properties applicationSDF.properties applicationSDF.properties.bak applicationSDG.properties applicationSDG.properties.bak applicationSDH.properties applicationSDS.properties applicationSDS.properties.bak; do
              az storage blob download \
                --connection-string "${{ secrets.AZ_STORAGE_CONFIG_CS_DEV }}" \
                --container-name "configs" \
                --name "property_files/sdg/$file" \
                --file "app/src/main/resources/$file"
            done

      - name: Build with Maven
        run: mvn -f ./app/pom.xml clean package

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Log in to container registry
        uses: docker/login-action@v1
        with:
          registry: acrsd1dev.azurecr.io
          username: acrsd1dev
          password: ${{ secrets.ACR_PASSWORD_DEV }}

      - name: Build and push container image to registry
        uses: docker/build-push-action@v2
        with:
          push: true
          tags: acrsd1dev.azurecr.io/sd1-backend:${{ env.BUILD_VERSION }}
          file: ./app/Dockerfile
          context: ./app

      - name: Login to k8s cluster
        uses: Maersk-Global/github-actions-commons/kubectl-login@main
        with:
          k8s-cluster: ane-dev-shared-eu2-01
          vault-name: sd1-kv
          vault-role-id: 87d65b80-5aba-9e21-e4f7-08e0d1949ffd
          vault-role-secret: ${{ secrets.VAULT_ROLE_SECRET }}

      - name: Update deployment resource and deploy
        run: |
          sed -i "s|BUILD_VERSION_PLACEHOLDER|${{ env.BUILD_VERSION }}|g" ./k8s/dev/deploy-modulesdg.yaml
          kubectl apply -k ./k8s/dev/

      - name: Wait for rollout to complete
        run: |
          echo "Waiting for deployment rollout to complete..."
          kubectl rollout status deployment/modulesdg -n sd1-dev --timeout=420s || {
            echo "❌ Rollout failed. Dumping recent events for debugging:"
            kubectl get events -n sd1-dev --sort-by='.lastTimestamp' | tail -n 20
            exit 1
          }
