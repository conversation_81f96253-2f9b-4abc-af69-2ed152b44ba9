package com.maersk.sd1.sdg.legacy.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.maersk.sd1.sdg.legacy.bean.PlannigGateInAssignmentAndAprroveInRequest;
import com.apm.business.externalService.integration.ServiceConnection;

import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.JpoUtil;
import ohSolutions.ohRest.util.bean.Response;

@RestController
@RequestMapping("/module/sdg/SDGtruckDepartureServiceImp")
@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDGtruckDepartureServiceImp extends SDGtruckDepartureService {

	SDGAssignmentGateOutServiceImp assigment = new SDGAssignmentGateOutServiceImp();
	String message = "";
	
	@RequestMapping(value = "/sdgtruckDepartureGet", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdgtruckDepartureGet(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdgtruckDepartureGet(ppo, request);
	}

	@RequestMapping(value = "/sdgtruckDepartureList", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdgtruckDepartureList(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdgtruckDepartureList(ppo, request);
	}
	
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/sdgtruckDepartureDelete", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdgtruckDepartureDelete(Jpo ppo, HttpServletRequest request) throws Exception {
		
		Response myResponse = new Response();

		SDGEIRServiceImp eirService = new SDGEIRServiceImp();
		
		Response eirServiceResponse = (Response) eirService.sdgeirDeleteBeforeyardv(ppo, request);
		
		Map<String, Object> eirServiceResult =  (Map<String, Object>) eirServiceResponse.getResult(); // {resp_result, resp_message, resp_flag_requireplanapprove, resp_gatein_eir_id}
		
		boolean flag_executeDeparture = false;
		boolean flag_requirePlanApprove = false;
		
		if(eirServiceResult.get("resp_result").equals(1)) {
		
			if(eirServiceResult.get("resp_flag_requireplanapprove").equals(1)) {
				
				flag_requirePlanApprove = true;
				
			}
			
			flag_executeDeparture = true;
			
		} else {
			
			myResponse = mapError(""+eirServiceResult.get("resp_message"));
			
		}
		
		if(flag_executeDeparture) {
			
			myResponse = (Response) super.sdgtruckDepartureDelete(ppo, request);
			
			if(flag_requirePlanApprove) {
				
				int eir_gatein = Integer.parseInt(""+eirServiceResult.get("resp_gatein_eir_id"));
				
				//MAKE SDY INTEGRATION CALL				
				ServiceConnection sdyConnection = new ServiceConnection(
					JpoUtil.getPropertie("SDG", "msk.api.apiUserLogin.sdy.loginUrl"),
					JpoUtil.getPropertie("SDG", "msk.api.apiUserLogin.sdy.user"),
					JpoUtil.getPropertie("SDG", "msk.api.apiUserLogin.sdy.password"),
					JpoUtil.getPropertie("SDG", "msk.api.apiUserLogin.sdy.system")
				);
				
				try {

					PlannigGateInAssignmentAndAprroveInRequest yardgisi = new PlannigGateInAssignmentAndAprroveInRequest();
					
					yardgisi.setEir_id(eir_gatein);
					yardgisi.setLanguage_id(Integer.parseInt(ppo.getData("SDG", "language_id")));
					yardgisi.setSub_business_unit_local_id(Integer.parseInt(ppo.getData("SDG", "sub_business_unit_local_id")));
					yardgisi.setUser_modification_id(""+ppo.getData("SDG", "user_modification_id"));

					Response resp = sdyConnection.post(JpoUtil.getPropertie("SDG", "msk.api.apiUserLogin.sdy.plannigGateInAssignmentAndAprrove"), yardgisi.toJSON("SDY"));					

					if (resp.isCorrect()) {

						if(resp.getMessage().equals("BusinessError")) {
							
							myResponse = mapError(""+resp.getResult());

						} else {
							
							ppo.commit();
							
						}
						
					} else {
						
						myResponse = mapError(""+resp.getResult());
						ppo.rollback();
						
					}
					
				} catch (Exception e) {
					
					e.printStackTrace();
					myResponse = mapError(e.getMessage());
					ppo.rollback();
					
				}
				
				
			} else {
				
				ppo.commit();
				
			}
			
		} else {
			
			ppo.rollback();
			
		}
		
		return myResponse;
		
	}
	
	private Response mapError(String message) {
		
		Response myResponse = new Response();
		
		List<Object> resultChild = new ArrayList<Object>();

		resultChild.add(0);
		resultChild.add(message);

		myResponse.setResult(resultChild);
		myResponse.setCorrect(true);
		
		return myResponse;
		
	}
	
	@SuppressWarnings("unchecked")
	public Object sdgtruckDepartureRegister(Jpo ppo, HttpServletRequest request) throws Exception {
		
		Response myResponse = new Response();
		
		SDGIntegrationServiceImp integration = new SDGIntegrationServiceImp();
		
		ppo.setData("SDG", "type_process", "gateout");

		Response checkInt = (Response) integration.sdyvalidateYardIntegration(ppo, request);
		
		List<Object> checklIntResponse = (List<Object>) checkInt.getResult();
		
		Map<String, Object> checkIntResponse = (Map<String, Object>) checklIntResponse.get(0);
		
		// DEV_SD1_0432
		//boolean flag_workorder = false;
		
		boolean flag_register = false;
		boolean flag_assigment = false;
		Map<String, Object> beforeYardServiceResult = new HashMap<String, Object>();
		
		try {
			
			Map<String, Object> queryResult = (Map<String, Object>) ppo.tabla("sde.eir").donde("eir_id = "+ppo.getData("SDG", "eir_id")).obtener("cat_movimiento_id");

			// 1.1. Validate movement gateout
			if(queryResult.get("cat_movimiento_id") != null && (""+queryResult.get("cat_movimiento_id")).equals(JpoUtil.getPropertie("SDG", "msk.cat.type_movement.gateout"))) {

				flag_assigment = true;
				
				// 1. Validate Yard integration
				if(checkIntResponse.get("result").equals(true)) {
					
					if(ppo.getData("SDG", "container_id") != null) {
						
							Response beforeYardService = (Response) super.sdgtruckDepartureRegisterBeforeyardv(ppo, request);
							
							try {
								beforeYardServiceResult = (Map<String, Object>) beforeYardService.getResult(); // {resp_result, resp_message, resp_flag_createworkorder, resp_container_number}

							} catch (Exception e) {
								// Possible conversion of result of confirm instruction movement.
								List<Object> beforeYardServiceResultList = (List<Object>) beforeYardService.getResult();
								
								Map<String, Object> beforeYardServiceResultListMap = (Map<String, Object>) beforeYardServiceResultList.get(0);
								
								if(beforeYardServiceResultListMap.get("resp_estado") != null && beforeYardServiceResultListMap.get("resp_estado").equals("BUSINESS_ERROR")) {
									beforeYardServiceResult = new HashMap<String, Object>();
									beforeYardServiceResult.put("resp_result", 0);
									beforeYardServiceResult.put("resp_message", beforeYardServiceResultListMap.get("resp_mensaje"));
								} else {
									beforeYardServiceResult = new HashMap<String, Object>();
									beforeYardServiceResult.put("resp_result", 1);
									beforeYardServiceResult.put("resp_message", beforeYardServiceResultListMap.get("resp_mensaje"));
								}
								
							}
							
							// 1.1.1 Validate before yard service OK
							if(beforeYardServiceResult.get("resp_result").equals(1)) {
								
								/*
								// 1.1.1.1 Validate before yard service require create work order
								if(beforeYardServiceResult.get("resp_flag_createworkorder") != null && beforeYardServiceResult.get("resp_flag_createworkorder").equals("1")){
									
									flag_workorder = true;
									
								}*/

								ppo.commit();
								flag_register = true;
								
							} else {
								
								myResponse = mapError(""+beforeYardServiceResult.get("resp_message"));
								flag_register = false;
								
							}
							
					} else {
						
						flag_register = true;
						
					}
					
				} else {
					
					flag_register = true;
					
				}
				
			} else {

				flag_assigment = false;
				flag_register = true;
				
			}
			
		} catch (Exception e) {
			
			e.printStackTrace();
			ppo.rollback();
			
		}
		
		/*
		if(flag_workorder) {
			
			String containerNumber = (String) beforeYardServiceResult.get("resp_container_number");
			Object empty_full_description = beforeYardServiceResult.get("resp_empty_full_alias"); // E | F
			
			ServiceConnection sdyConnection = new ServiceConnection(
					JpoUtil.getPropertie("SDG", "msk.api.apiUserLogin.sdy.loginUrl"),
					JpoUtil.getPropertie("SDG", "msk.api.apiUserLogin.sdy.user"),
					JpoUtil.getPropertie("SDG", "msk.api.apiUserLogin.sdy.password"),
					JpoUtil.getPropertie("SDG", "msk.api.apiUserLogin.sdy.system"));
			
			try {
				
				//CREATE JSON OBJECT TO SEND TO SDY RESTPOINT
				JSONObject body_sdy = new JSONObject();
				JSONObject body_f = new JSONObject();
				JSONObject body = new JSONObject();
				JSONArray containers_array = new JSONArray().put(new JSONObject()
						.put("container_number", containerNumber)
						.put("eir_number", Integer.parseInt(ppo.getData("SDG", "eir_id"))));
				
				body.put("user_id", ppo.getData("SDG", "user_registration_id"))
					.put("operation_type", "CLNTE")
					.put("business_unit_id", Integer.parseInt(ppo.getData("SDG", "sub_business_unit_local_id")))
					.put("containers", containers_array)
					.put("containers_assignment", false)
					.put("booking_id", 0)
					.put("cat_container_size_id", 0)
					.put("cat_container_type_id", 0)
					.put("cat_container_class_id", 0)
					.put("cat_container_content_type_id", 0)
					.put("pick_up_quantity", 1)
					.put("local_sub_business_unit_id", Integer.parseInt(ppo.getData("SDG", "sub_business_unit_local_id")))
					.put("container_content_type", empty_full_description)
					.put("documento_carga_id", 0)
					.put("containers_related", false);
					
				body_f.put("F", body);
				body_sdy.put("SDY", body_f);
				
				String _body = body_sdy.toString();
				System.out.println(_body);
				
				//PERFORM SERVICE CALL (POST)
				RequestBodyEntity resp;
				resp = Unirest.post
						(JpoUtil.getPropertie("SDG", "msk.api.apiUserLogin.sdy.afterTruckDepartureCreateWorkOrder")).body(_body)
						.header("Authorization", "Bearer " + sdyConnection.getTokenAPI())
						.header("Content-type", "application/json");
				HttpResponse<String> response_integration = resp.asString();
				JSONObject json_response = new JSONObject(response_integration.getBody());
				
				if (json_response.get("isCorrect").equals(true) && json_response.get("result") != null) {
					
					flag_register = true;
					
				} else {
					
					myResponse = mapError(json_response.get("result") != null ? ""+json_response.get("result") : "Error processing yard service");

				}
				
			} catch (Exception e) {
				e.printStackTrace();
				myResponse = mapError(e.getMessage());
			}
			
		}
		*/

		boolean rpta_assigment = false;
		
		if(flag_register && flag_assigment) {
			
			rpta_assigment = this.implementAssigmentGateOut(ppo, request);
			
			if(!rpta_assigment) {
				myResponse = mapError("Assignment Gate Out : " + this.message);
			}
			
		} else {
			
			rpta_assigment = true;
			
		}
		
		if(flag_register && rpta_assigment) {
	
			myResponse = (Response) super.sdgtruckDepartureRegister(ppo, request);
			
			List<Object> checkLMyResponse = (List<Object>) myResponse.getResult();
			int codeReturn = (int) checkLMyResponse.get(0);
			String messageReturn = (String) checkLMyResponse.get(1);
			
			System.out.println(checkLMyResponse);
			System.out.println(codeReturn);
			System.out.println(messageReturn);
			
			if(codeReturn == 1){
				ppo.commit();
			}else{
				ppo.rollback();
			}
			
			
			//ppo.commit();
			
		} else {
			
			ppo.rollback();
			
		}
		
		return myResponse;
		
	}
	
	@SuppressWarnings("unchecked")
	private boolean implementAssigmentGateOut(Jpo ppo, HttpServletRequest request) throws Exception {
		
		String is_for_gate_out_assignation = ppo.getData("SDG", "is_for_gate_out_assignation");
		String eir_id = ppo.getData("SDG", "eir_id");
		String container_id = ppo.getData("SDG", "container_id");
		String chassis_id = ppo.getData("SDG", "chassis_id");
		
		System.out.println("is_for_gate_out_assignation => " + is_for_gate_out_assignation);
		System.out.println("eir_id => " + eir_id);
		System.out.println("container_id => " + container_id);
		System.out.println("chassis_id => " + chassis_id);
		
		Response responseAssignGout;
		if(is_for_gate_out_assignation.equals("1")) {
			responseAssignGout = (Response) assigment.gateoutGeneralAssignmentRegister(ppo, request, false, true);
			List<Object> resultAssignGout = (List<Object>) responseAssignGout.getResult();
			
			if(responseAssignGout != null && responseAssignGout.isCorrect() && resultAssignGout.get(0).equals(1)) {
				return true;
			}else {
				System.out.println(resultAssignGout.get(1));
				this.message = (String) resultAssignGout.get(1);
				return false;
			}
		}else {
			return true;
		}
	}

}