package com.maersk.sd1.sde.service;

import com.maersk.sd1.sde.model.EirNotification;
import com.maersk.sd1.sde.repository.EirNotificationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class EirNotificationService {

    @Autowired
    private EirNotificationRepository repository;

    public List<EirNotification> getAllNotifications() {
        return repository.findAll();
    }

    public Optional<EirNotification> getNotificationById(int id) {
        return repository.findById(id);
    }

    public List<EirNotification> findTop10ByStatusId(Integer statusId){
        return repository.findTop10ByStatusId(statusId);
    }

    public EirNotification createNotification(EirNotification notification) {
        return repository.save(notification);
    }

    public EirNotification updateNotification(int id, EirNotification notificationDetails) {
        return repository.findById(id).map(notification -> {
            notification.setEirId(notificationDetails.getEirId());
            notification.setStatusId(notificationDetails.getStatusId());
            notification.setActive(notificationDetails.isActive());
            notification.setUserRegistrationId(notificationDetails.getUserRegistrationId());
            notification.setRegistrationDate(notificationDetails.getRegistrationDate());
            notification.setUserModificationId(notificationDetails.getUserModificationId());
            notification.setModificationDate(notificationDetails.getModificationDate());
            notification.setAzureStorageUrl(notificationDetails.getAzureStorageUrl());
            return repository.save(notification);
        }).orElseThrow(() -> new RuntimeException("Notification not found with id " + id));
    }

    public void deleteNotification(int id) {
        repository.deleteById(id);
    }
}
