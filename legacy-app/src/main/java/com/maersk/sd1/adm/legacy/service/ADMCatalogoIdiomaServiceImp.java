package com.maersk.sd1.adm.legacy.service;

import javax.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Jpo;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/module/adm/ADMCatalogoIdiomaServiceImp")
@JpoClass(source = "dsinland", oauth2Enable = true)
public class ADMCatalogoIdiomaServiceImp extends ADMCatalogoIdiomaService {

	@RequestMapping(value = "/gescatalogoIdiomaRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object gescatalogoIdiomaRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.gescatalogoIdiomaRegistrar(ppo, request);
	}

	@RequestMapping(value = "/gescatalogoIdiomaObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object gescatalogoIdiomaObtener(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.gescatalogoIdiomaObtener(ppo, request);
	}

	@RequestMapping(value = "/gescatalogoIdiomaListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object gescatalogoIdiomaListar(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.gescatalogoIdiomaListar(ppo, request);
	}

	@RequestMapping(value = "/gescatalogoIdiomaEliminar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object gescatalogoIdiomaEliminar(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.gescatalogoIdiomaEliminar(ppo, request);
	}

	@RequestMapping(value = "/gescatalogoIdiomaEditar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object gescatalogoIdiomaEditar(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.gescatalogoIdiomaEditar(ppo, request);
	}

}