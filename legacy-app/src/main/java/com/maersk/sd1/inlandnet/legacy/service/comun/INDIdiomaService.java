package com.maersk.sd1.inlandnet.legacy.service.comun;

import javax.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class INDIdiomaService {

	@RequestMapping(value = "/segidiomaSistemaRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object segidiomaSistemaRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("seg.idioma_sistema_registrar","IND");
			pResult.input("usuario_id", Jpo.INTEGER);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.input("sistema_id", Jpo.INTEGER);
			pResult.output("resp_new_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}