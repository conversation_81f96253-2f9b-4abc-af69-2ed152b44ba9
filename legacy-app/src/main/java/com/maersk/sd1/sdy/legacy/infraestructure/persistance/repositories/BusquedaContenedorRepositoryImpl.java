package com.maersk.sd1.sdy.legacy.infraestructure.persistance.repositories;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Repository;

import com.maersk.sd1.sdy.legacy.core.planing.domain.BusquedaContenedorCommand;
import com.maersk.sd1.sdy.legacy.core.planing.domain.BusquedaContenedorResponse;
import com.maersk.sd1.sdy.legacy.core.planing.domain.InformacionContenedorCommand;
import com.maersk.sd1.sdy.legacy.core.planing.port.BusquedaContenedorRepository;
import com.maersk.sd1.sdy.legacy.core.shared.IntUtils;
import com.maersk.sd1.sdy.legacy.core.shared.StringUtils;
import com.maersk.sd1.sdy.legacy.infraestructure.dto.InformacionContenedorResponseDto;
import com.maersk.sd1.sdy.legacy.infraestructure.dto.InformacionContenedorResultQueryDto;
import com.maersk.sd1.sdy.legacy.infraestructure.dto.planificacion.BusquedaContenedorPorCriterioCommandDto;
import com.maersk.sd1.sdy.legacy.infraestructure.dto.planificacion.BusquedaContenedorPorCriterioResponseDto;
import com.maersk.sd1.sdy.legacy.infraestructure.dto.planificacion.TotalRegistrosDto;
import com.maersk.sd1.sdy.legacy.infraestructure.shared.utils.GsonUtil;

import lombok.RequiredArgsConstructor;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.Procedure;

@Repository
@RequiredArgsConstructor
public class BusquedaContenedorRepositoryImpl implements BusquedaContenedorRepository {

	@Autowired
	private ApplicationContext context;

	@Override
	public Collection<BusquedaContenedorResponse> getBusquedaContenedor(BusquedaContenedorCommand command)
			throws SQLException {

		List<BusquedaContenedorResponse> respuestaBusqueda = new ArrayList<BusquedaContenedorResponse>();
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_contenedor_buscar");
		try {
			pResult.input("numero_contenedores", StringUtils.getStringValue(command.getNumero_contenedores()),
					Jpo.STRING);
			pResult.input("cat_familia_id", StringUtils.getStringValue(command.getCat_familia_id()), Jpo.INTEGER);
			pResult.input("cat_tamano_id", StringUtils.getStringValue(command.getCat_tamano_id()), Jpo.INTEGER);
			pResult.input("cat_tipo_contenedor_id", StringUtils.getStringValue(command.getCat_tipo_contenedor_id()),
					Jpo.INTEGER);
			pResult.input("linea_naviera_id", StringUtils.getStringValue(command.getLinea_naviera_id()), Jpo.INTEGER);
			pResult.input("nave_id", StringUtils.getStringValue(command.getNave_id()), Jpo.INTEGER);
			pResult.input("puerto_descarga_id", StringUtils.getStringValue(command.getPuerto_descarga_id()),
					Jpo.INTEGER);
			pResult.input("cat_clase_id", StringUtils.getStringValue(command.getCat_clase_id()), Jpo.INTEGER);
			pResult.input("cat_tipo_reefer_id", StringUtils.getStringValue(command.getCat_tipo_reefer_id()),
					Jpo.INTEGER);
			pResult.input("group_code", StringUtils.getStringValue(command.getGroup_code()), Jpo.STRING);
			pResult.input("cat_categoria_id", StringUtils.getStringValue(command.getCat_categoria_id()), Jpo.STRING);
			pResult.input("tipo_operacion_id", StringUtils.getStringValue(command.getTipo_operacion_id()), Jpo.STRING);
			pResult.input("patio_id", StringUtils.getStringValue(command.getPatio_id()), Jpo.STRING);
			pResult.input("tiene_preasignacion", StringUtils.getStringValue(command.getTiene_preasignacion()),
					Jpo.STRING);
			pResult.input("tiene_restriccion", StringUtils.getStringValue(command.getTiene_restriccion()), Jpo.STRING);
			pResult.input("numero_documento_ingreso", StringUtils.getStringValue(command.getNumero_documento_ingreso()),
					Jpo.STRING);
			pResult.input("numero_documento_salida", StringUtils.getStringValue(command.getNumero_documento_salida()),
					Jpo.STRING);
			pResult.input("carga_maxima", StringUtils.getStringValue(command.getCarga_maxima()), Jpo.INTEGER);

			pResult.input("unidad_negocio_id", IntUtils.getStringValue(command.getUnidad_negocio_id()), Jpo.INTEGER);
			pResult.input("ordenamiento_columna", StringUtils.getStringValue(command.getOrdenamiento_columna()),
					Jpo.STRING);
			pResult.input("ordenamiento_direccion", StringUtils.getStringValue(command.getOrdenamiento_direccion()),
					Jpo.STRING);
			pResult.input("page", command.getPage(), Jpo.INTEGER);
			pResult.input("size", command.getSize(), Jpo.INTEGER);
			
			//To add language support
			pResult.input("language_id", StringUtils.getStringValue(command.getLanguage_id()), Jpo.INTEGER);
			
			pResult.input("owner", StringUtils.getStringValue(command.getOwner()), Jpo.STRING);
			pResult.input("emr_condition", StringUtils.getStringValue(command.getEmr_condition()), Jpo.STRING);

			List<Object> resultado = (List<Object>) pResult.execute();

			TotalRegistrosDto totalregistros = GsonUtil.GetModel(resultado.get(0), TotalRegistrosDto.class);
			int total = totalregistros.getTotal_registros();
			command.setTotal(total);

			respuestaBusqueda = GsonUtil.GetList(resultado.get(1), BusquedaContenedorResponse.class);

			conn.commit();
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		} finally {
			
		}
		return respuestaBusqueda;
	}

	@Override
	public InformacionContenedorResponseDto getInformacionContenedor(InformacionContenedorCommand request)
			throws Exception {

		var respuestaBusqueda = new InformacionContenedorResponseDto();
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_informacion_contenedor");
		try {
			pResult.input("patio_codigo", request.getPatio_codigo(), Jpo.STRING);
			pResult.input("numero_contenedor", request.getNumero_contenedor(), Jpo.STRING);

			List<Object> resultado = (List<Object>) pResult.execute();

			Collection<InformacionContenedorResultQueryDto> response = GsonUtil.GetList(resultado,
					InformacionContenedorResultQueryDto.class);
			if (response.isEmpty()) {
				respuestaBusqueda = InformacionContenedorResponseDto.NotFound(request.getNumero_contenedor());
//				throw new Exception("Contenedor no encontrado");
			} else {
				InformacionContenedorResultQueryDto first = (InformacionContenedorResultQueryDto) response.toArray()[0];
				InformacionContenedorResultQueryDto second = response.size() > 1
						? (InformacionContenedorResultQueryDto) response.toArray()[1]
						: null;

				respuestaBusqueda = new InformacionContenedorResponseDto(first.getContenedor_id(),
						first.getNumero_contenedor(), first.getBloque_tipo(), first.getBloque_codigo(), first.getFila(),
						first.getColumna(), first.getNivel(), second != null ? second.getFila() : null,
						second != null ? second.getColumna() : null, second != null ? second.getNivel() : null);
			}

			conn.commit();
		} catch (Exception e) {
			conn.finalizar();
			throw new Exception(e.getMessage());
		}
		return respuestaBusqueda;
	}

	public Collection<BusquedaContenedorPorCriterioResponseDto> getBuscarContenedoresPorCriterio(
			BusquedaContenedorPorCriterioCommandDto command) throws SQLException {
		List<BusquedaContenedorPorCriterioResponseDto> respuestaBusqueda = new ArrayList<BusquedaContenedorPorCriterioResponseDto>();
		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_contenedor_buscar_por_criterio");
		try {
			pResult.input("numero_contenedores", StringUtils.getStringValue(command.getNumero_contenedor()),
					Jpo.STRING);
			pResult.input("unidad_negocio_id", IntUtils.getStringValue(command.getUnidad_negocio_id()), Jpo.INTEGER);

			List<Object> resultado = (List<Object>) pResult.execute();
			respuestaBusqueda = GsonUtil.GetList(resultado, BusquedaContenedorPorCriterioResponseDto.class);

			conn.commit();
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return respuestaBusqueda;
	}

}
