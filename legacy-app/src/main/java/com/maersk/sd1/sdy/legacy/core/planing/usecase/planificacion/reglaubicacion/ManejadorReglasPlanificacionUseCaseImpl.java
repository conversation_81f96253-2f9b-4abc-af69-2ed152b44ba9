package com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.reglaubicacion;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.maersk.sd1.sdy.legacy.core.planing.domain.Bloque;
import com.maersk.sd1.sdy.legacy.core.planing.domain.ReglaPlanificacionPatio;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.CriterioBusquedaReglaContenedor;
import com.maersk.sd1.sdy.legacy.core.planing.port.ReglasUbicacionRepository;
import com.maersk.sd1.sdy.legacy.infraestructure.shared.utils.GsonUtil;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class ManejadorReglasPlanificacionUseCaseImpl implements ManejadorReglasPlanificacionUseCase {

	private final ReglasUbicacionRepository reglasUbicacionRepository; 
	private final BuscarReglaCorrespondienteUseCase buscador;
	
	private Collection<ReglaPlanificacionPatio> reglas = new ArrayList<ReglaPlanificacionPatio>();

	public ManejadorReglasPlanificacionUseCaseImpl(ReglasUbicacionRepository reglasUbicacionRepository,
			BuscarReglaCorrespondienteUseCase buscarReglaPlanificacionUseCase) {
		this.reglasUbicacionRepository = reglasUbicacionRepository;
		this.buscador = buscarReglaPlanificacionUseCase;
	}
	@Override
	public ReglaPlanificacionPatio BuscarRegla(CriterioBusquedaReglaContenedor criterio) {
		return buscador.Buscar(criterio, reglas);
	}

	@Override
	public void CargarReglasPatio(int patio_id) throws SQLException {
		reglas = reglasUbicacionRepository.TraerReglasDelPatio(patio_id);
	}

	@Override
	public void CargarReglasBloque(Bloque bloque_destino) throws SQLException {
		reglas = reglasUbicacionRepository.TraerReglasDelBloque(bloque_destino.getBloque_id());
	}
	
	@Override
	public Collection<ReglaPlanificacionPatio> FindRulesByCriteriaInBlock(Collection<CriterioBusquedaReglaContenedor> rulesCriteria, Integer blockId) throws SQLException {
		String rulesCriteria_json = GsonUtil.ConvertToJSON(rulesCriteria);		
		return reglasUbicacionRepository.FindRulesByCriteriaInBlock(rulesCriteria_json, blockId);
		
	}
	
	@Override
	public Collection<ReglaPlanificacionPatio> FindRulesByCriteriaInYard(Collection<CriterioBusquedaReglaContenedor> rulesCriteria, Integer yardId) throws SQLException {
		String rulesCriteria_json = GsonUtil.ConvertToJSON(rulesCriteria);		
		return reglasUbicacionRepository.FindRulesByCriteriaInYard(rulesCriteria_json, yardId);
		
	}
	@Override
	public Collection<ReglaPlanificacionPatio> FindRulesByCriteriaInBlock(
			Collection<CriterioBusquedaReglaContenedor> rulesCriteria, Integer blockId, Collection<Integer> skipRows,
			Integer skipBlockId) throws SQLException {
		String rulesCriteria_json = GsonUtil.ConvertToJSON(rulesCriteria);		
		return reglasUbicacionRepository.FindRulesByCriteriaInBlock(rulesCriteria_json, blockId, skipRows, skipBlockId);
	}
	@Override
	public Collection<ReglaPlanificacionPatio> FindRulesByCriteriaInYard(
			Collection<CriterioBusquedaReglaContenedor> rulesCriteria, Integer yardId, Collection<Integer> skipRows,
			Integer skipBlockId) throws SQLException {
		String rulesCriteria_json = GsonUtil.ConvertToJSON(rulesCriteria);		
		return reglasUbicacionRepository.FindRulesByCriteriaInYard(rulesCriteria_json, yardId, skipRows, skipBlockId);
	}
	
}
