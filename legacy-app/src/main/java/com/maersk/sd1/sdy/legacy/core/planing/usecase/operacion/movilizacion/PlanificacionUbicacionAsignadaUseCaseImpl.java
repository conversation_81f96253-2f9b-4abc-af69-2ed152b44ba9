package com.maersk.sd1.sdy.legacy.core.planing.usecase.operacion.movilizacion;

import org.springframework.beans.factory.annotation.Autowired;

import com.maersk.sd1.sdy.legacy.core.planing.domain.ColaTrabajo;
import com.maersk.sd1.sdy.legacy.core.planing.domain.Tipo;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.DatosPlanificacion;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.PlanificacionRequestCommand;
import com.maersk.sd1.sdy.legacy.core.planing.port.BloqueRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.ColaTrabajoRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.ContenedorRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.InstruccionMovimientoRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.OperacionRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.ReglasUbicacionRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.UbicacionContenedorRepository;
import com.maersk.sd1.sdy.legacy.core.planing.port.UsuarioRepository;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.InstruccionMovimientoBuilderUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.ActualizarSecuenciaAColaTrabajoUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.ColasTrabajoUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.DeterminarUbicacionOrigenUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.EstrategiaPlanificacion;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.PlanificacionBaseUseCase;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.SeleccionUbicacionDisponible;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.movilizacion.PlanificacionMovilizacionUseCaseImpl;
import com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion.reglaubicacion.ObtenerReglasPlanificacionUseCase;

public class PlanificacionUbicacionAsignadaUseCaseImpl 
	extends PlanificacionMovilizacionUseCaseImpl
	implements PlanificacionBaseUseCase {

	@Autowired
	private ColaTrabajoRepository colaTrabajoRepository;

	private static Tipo Tipo_Movimiento_Rehandling = new Tipo(42906, null, "REH", "REMOVIDO", true);
	
	public PlanificacionUbicacionAsignadaUseCaseImpl(BloqueRepository bloqueRepository,
			UbicacionContenedorRepository ubicacionContenedorRepository, 
			ContenedorRepository contenedorRepository,
			InstruccionMovimientoRepository instruccionMovimientoRepository, 
			OperacionRepository operacionRepository,
			UsuarioRepository usuarioRepository, 
			ReglasUbicacionRepository reglasUbicacionRepository,
			ColasTrabajoUseCase colaTrabajoUseCase, 
			SeleccionUbicacionDisponible seleccionUbicacionDisponible,
			InstruccionMovimientoBuilderUseCase instruccionMovimientoBuilderUseCase,
			ObtenerReglasPlanificacionUseCase obtenerReglasPlanificacionUseCase,
			EstrategiaPlanificacion asignadorPorUbicacionBase,
			EstrategiaPlanificacion asignadorPorUbicacionSeleccionada,
			EstrategiaPlanificacion asignadorPorUbicacionPorRegla, 
			ColasTrabajoUseCase colasTrabajoUseCase,
			ActualizarSecuenciaAColaTrabajoUseCase actualizarSecuenciaIntruccionMovimientoUseCase,
			DeterminarUbicacionOrigenUseCase determinarUbicacionOrigenUseCase) {
		super(bloqueRepository, ubicacionContenedorRepository, contenedorRepository, instruccionMovimientoRepository,
				operacionRepository, usuarioRepository, reglasUbicacionRepository, colaTrabajoUseCase,
				seleccionUbicacionDisponible, instruccionMovimientoBuilderUseCase, obtenerReglasPlanificacionUseCase,
				asignadorPorUbicacionBase, asignadorPorUbicacionSeleccionada, asignadorPorUbicacionPorRegla,
				colasTrabajoUseCase, actualizarSecuenciaIntruccionMovimientoUseCase, determinarUbicacionOrigenUseCase);
		// TODO Auto-generated constructor stub
	}

	@Override
	public DatosPlanificacion completarDatosPlanificacion(PlanificacionRequestCommand request) throws Exception {
		var datos = super.completarDatosPlanificacion(request);
		
		datos.setTipo_movimiento_interno(Tipo_Movimiento_Rehandling);

		var cola = colaTrabajoRepository.TraerPorId(request.getCola_trabajo_id());
		
		datos.setCola_trabajo(cola);
		return datos;
	}
	
	@Override
	public ColaTrabajo seleccionarColaTrabajo(DatosPlanificacion datos) {
		return datos.getCola_trabajo();
	}
}
