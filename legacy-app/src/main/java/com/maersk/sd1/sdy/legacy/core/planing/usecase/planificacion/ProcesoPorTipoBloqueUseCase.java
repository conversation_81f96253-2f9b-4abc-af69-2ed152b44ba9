package com.maersk.sd1.sdy.legacy.core.planing.usecase.planificacion;

import java.util.Collection;

import com.maersk.sd1.sdy.legacy.core.planing.domain.UbicacionContenedor;
import com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion.DatosPlanificacion;

public interface ProcesoPorTipoBloqueUseCase {
	public Collection<UbicacionContenedor> procesar(DatosPlanificacion datos) throws Exception;
}
