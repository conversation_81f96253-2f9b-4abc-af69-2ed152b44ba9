package com.maersk.sd1.sdy.legacy.core.planing.domain.planificacion;

import com.maersk.sd1.sdy.legacy.infraestructure.delivery.responses.SDYRequestAssignmentGateOut;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@Builder
@Data
@NoArgsConstructor
public class AssignmentGateOutRequest {
	@ApiModelProperty(notes = "Sub Business Unit Local ID", required = true) 
	private Integer sub_business_unit_local_id;
	@ApiModelProperty(notes = "EIR ID", required = true) 
	private Integer eir_id;
	@ApiModelProperty(notes = "Container ID", required = true) 
	private Integer container_id;

	@ApiModelProperty(notes = "Photos", required = true)
	private String photos;

	@ApiModelProperty(notes = "Seal 1", required = true)
	private String seal_1;
	@ApiModelProperty(notes = "Seal 2", required = true)
	private String seal_2;
	@ApiModelProperty(notes = "Seal 3", required = true)
	private String seal_3;
	@ApiModelProperty(notes = "Seal 4", required = true)
	private String seal_4;

	@ApiModelProperty(notes = "Language ID", required = true)
	private Integer languaje_id;

	@ApiModelProperty(notes = "Comments", required = true)
	private String comments;
	@ApiModelProperty(notes = "Operation Code", required = true)
	private String operation_code;

	@ApiModelProperty(notes = "Chassis ID", required = true)
	private Integer chassis_id;
	@ApiModelProperty(notes = "Document Load Detail ID", required = true)
	private Integer documento_carga_detalle_id;
	@ApiModelProperty(notes = "Planning Detail ID", required = true)
	private Integer planning_detail_id;

	/* Filds required for sdyPlannigGateOut */
	@ApiModelProperty(notes = "User Alias", required = true)
	private String user_alias;
	@ApiModelProperty(notes = "Operation Type", required = true)
	private String operation_type;

	
	public static AssignmentGateOutRequest CreateGateOutFrom(SDYRequestAssignmentGateOut<AssignmentGateOutRequest> request) {
		var plan_request = AssignmentGateOutRequest.builder()
				.sub_business_unit_local_id(request.getSDY().getF().getSub_business_unit_local_id())
				.eir_id(request.getSDY().getF().getEir_id())
				.container_id(request.getSDY().getF().getContainer_id())

				.photos(request.getSDY().getF().getPhotos())

				.seal_1(request.getSDY().getF().getSeal_1())
				.seal_2(request.getSDY().getF().getSeal_2())
				.seal_3(request.getSDY().getF().getSeal_3())
				.seal_4(request.getSDY().getF().getSeal_4())

				.languaje_id(request.getSDY().getF().getLanguaje_id())

				.comments(request.getSDY().getF().getComments())
				.operation_code(request.getSDY().getF().getOperation_code())


				.chassis_id(request.getChassis_id())
				.documento_carga_detalle_id(request.getDocumento_carga_detalle_id())
				.planning_detail_id(request.getPlanning_detail_id())


				.user_alias(request.getSDY().getF().getUser_alias())
				.operation_type(request.getSDY().getF().getOperation_type())

				.build();

		return plan_request;
	}

}
