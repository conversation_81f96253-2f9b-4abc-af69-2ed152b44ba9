package com.maersk.sd1.sds.legacy.sds;

import javax.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDSPrincipalService {

	@RequestMapping(value = "/sdsdatosGenerales", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsdatosGenerales(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.datos_generales","SDS");
			pResult.input("Unidad_negocio_id", Jpo.INTEGER);
			pResult.input("Usuario_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdsobtenerCatalogoPorTablas", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdsobtenerCatalogoPorTablas(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.obtener_catalogo_por_tablas","SDS");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("idioma_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdspictureMarkGet", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdspictureMarkGet(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.picture_mark_get","SDS");
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdstemplateGet", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdstemplateGet(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sds.template_get","SDS");
			pResult.input("template_name", Jpo.STRING);
			pResult.input("language_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}