// Additional imports needed:
import java.time.temporal.ChronoUnit;
import java.util.Objects;

// Additional repository fields to add to the class:
    private final DepotCredentialAppeirRepository depotCredentialAppeirRepository;
    private final EirSendAppeirRepository eirSendAppeirRepository;

// Additional repository method needed in DepotCredentialAppeirRepository:
@Query("""
    SELECT dca FROM DepotCredentialAppeir dca
    WHERE dca.subBusinessUnit.id = :subBusinessUnitId
    AND dca.shippingLineId = :shippingLineId
    AND dca.sendGateIn = true
    AND dca.active = true
    """)
Optional<DepotCredentialAppeir> findBySubBusinessUnitAndShippingLineAndSendGateInAndActive(
    @Param("subBusinessUnitId") Integer subBusinessUnitId,
    @Param("shippingLineId") Integer shippingLineId
);
