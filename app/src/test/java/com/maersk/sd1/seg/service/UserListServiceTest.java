package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.User;
import com.maersk.sd1.seg.dto.UserListInput;
import com.maersk.sd1.seg.dto.UserListOutput;
import com.maersk.sd1.seg.repository.UserListRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.PageImpl;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

class UserListServiceTest {

    @Mock
    private UserListRepository userListRepository;

    @InjectMocks
    private UserListService userListService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    @Test
    void GivenNoUserIds_WhenListingUsers_ThenReturnsFilteredUserList() {

        UserListInput.Input inputParams = new UserListInput.Input();
        inputParams.setPage(1);
        inputParams.setSize(10);

        User user = new User();
        user.setId(1);
        user.setAlias("USR1");
        user.setMail("<EMAIL>");
        user.setNames("John Doe");
        user.setStatus('1');

        List<User> userList = new ArrayList<>();
        userList.add(user);
        Pageable pageable = PageRequest.of(0, 10);
        Page<User> userPage = new PageImpl<>(userList, pageable, 1);

        when(userListRepository.findByAllFilters(any(), any(), any(), any(), any(), any(), any(), any(), eq(pageable)))
                .thenReturn(userPage);

        UserListOutput output = userListService.listUsers(inputParams);

        assertNotNull(output);
        assertEquals(1, output.getUsers().size());
        assertEquals(List.of(List.of(Long.valueOf("1"))), output.getTotalRecords());
        assertEquals("USR1", output.getUsers().getFirst().getAlias());
        assertEquals("<EMAIL>", output.getUsers().getFirst().getEmail());
    }

    @Test
    void GivenUserIdsThatDoNotExist_WhenListingUsers_ThenReturnsEmptyUserList() {

        UserListInput.Input inputParams = new UserListInput.Input();
        inputParams.setUserIds("9999");
        inputParams.setPage(1);
        inputParams.setSize(10);

        List<User> userList = new ArrayList<>();
        Pageable pageable = PageRequest.of(0, 10);
        Page<User> userPage = new PageImpl<>(userList, pageable, 0);

        when(userListRepository.findByAllFilters(any(), any(), any(), any(), any(), any(), any(), any(), eq(pageable)))
                .thenReturn(userPage);

        UserListOutput output = userListService.listUsers(inputParams);

        assertNotNull(output);
        assertEquals(0, output.getUsers().size());
        assertEquals(List.of(List.of(Long.valueOf("0"))), output.getTotalRecords());
    }

    @Test
    void GivenRepositoryThrowsError_WhenListingUsers_ThenReturnsErrorResponse() {
        UserListInput.Input inputParams = new UserListInput.Input();
        inputParams.setUserIds("1,2,46188");

        when(userListRepository.findByAllFilters(any(), any(), any(), any(), any(), any(), any(), any(), any()))
                .thenThrow(new RuntimeException("Database error"));

        UserListOutput output = userListService.listUsers(inputParams);

        assertNotNull(output);
    }
}
