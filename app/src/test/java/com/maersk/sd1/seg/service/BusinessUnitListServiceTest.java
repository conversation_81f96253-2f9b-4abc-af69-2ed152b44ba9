package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.repository.BusinessUnitListRepository;
import com.maersk.sd1.seg.controller.dto.BusinessUnitConfigDTO;
import com.maersk.sd1.seg.controller.dto.BusinessUnitCurrencyDTO;
import com.maersk.sd1.seg.controller.dto.BusinessUnitListTempDTO;
import com.maersk.sd1.seg.dto.BusinessUnitListInput;
import com.maersk.sd1.seg.dto.BusinessUnitListOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BusinessUnitListServiceTest {

    @Mock
    private BusinessUnitListRepository businessUnitListRepository;

    @InjectMocks
    private BusinessUnitListService businessUnitListService;

    private BusinessUnitListInput.Input inputParams;
    private List<BusinessUnitListTempDTO> mockBusinessUnits;

    @BeforeEach
    void setUp() {
        inputParams = new BusinessUnitListInput.Input();
        inputParams.setParentUnit(1);
        inputParams.setUnitType(2);
        inputParams.setParentIndicator("1");
        inputParams.setPage(1);
        inputParams.setSize(10);

        BusinessUnitListTempDTO businessUnit = new BusinessUnitListTempDTO(
                1, "Finance", true, null, "Root", 100, "ERP", "icon.png",
                Arrays.asList(new BusinessUnitConfigDTO(1, 101, "ConfigValue", true)).toString(),
                Arrays.asList(new BusinessUnitCurrencyDTO(1, 200, "USD")).toString(),
                "FinanceAlias"
        );
        mockBusinessUnits = List.of(businessUnit);
    }

    @Test
    void Given_NoBusinessUnitsAvailable_When_ListBusinessUnitsIsCalled_Then_EmptyListIsReturned() {
        when(businessUnitListRepository.findBusinessList(1, 2)).thenReturn(Collections.emptyList());

        BusinessUnitListOutput result = businessUnitListService.listBusinessUnits(inputParams);

        assertNotNull(result);
        assertEquals(0, result.getTotalRecords().getFirst().getFirst());
        assertTrue(result.getBusinessUnits().isEmpty());
    }

    @Test
    void Given_DatabaseErrorOccurs_When_ListBusinessUnitsIsCalled_Then_ErrorResponseIsReturned() {
        when(businessUnitListRepository.findBusinessList(1, 2)).thenThrow(new RuntimeException("Database error"));

        BusinessUnitListOutput result = businessUnitListService.listBusinessUnits(inputParams);

        assertNotNull(result);
        assertEquals(0, result.getTotalRecords().getFirst().getFirst());
        assertTrue(result.getBusinessUnits().isEmpty());
    }
}
