package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sde.dto.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EirGetServiceTest {

    @Mock
    private EirRepository eirRepository;

    @Mock
    private EirMultipleRepository eirMultipleRepository;

    @Mock
    private ContainerRepository containerRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;

    @Mock
    private BusinessUnitRepository businessUnitRepository;

    @Mock
    private ContainerRestrictionRepository containerRestrictionRepository;

    @Mock
    private ConteconLogRepository conteconLogRepository;

    @Mock
    private CatalogLanguageRepository catalogLanguageRepository;

    @Mock
    private EirActivityZoneRepository eirActivityZoneRepository;

    @Mock
    private EirZoneRepository eirZoneRepository;

    @Mock
    private EirObservationInspectorRepository eirObservationInspectorRepository;

    @Mock
    private GateTransmissionRepository gateTransmissionRepository;

    @InjectMocks
    private EirGetService eirGetService;

    private final Integer eirId = 1;
    private final Integer languageId = 1;
    private final Integer containerDummyId = 1;
    private final Integer subBusinessUnitId = 1;
    private final Integer isFull = 1;
    private final Integer idPreGateReception = 1;

    @BeforeEach
    void setUp() {
        lenient().when(eirRepository.findEirData(anyInt(), anyInt(), anyInt(), any(), any(), anyInt(), anyInt(), anyInt()))
                .thenReturn(Collections.emptyList());
        lenient().when(containerRepository.findContainerDummyId()).thenReturn(containerDummyId);
        lenient().when(catalogRepository.findIdByAlias(anyString())).thenReturn(isFull);
        lenient().when(eirDocumentCargoDetailRepository.findDocumentsByEirId(anyInt())).thenReturn(Collections.emptyList());
        lenient().when(businessUnitRepository.getFormatoDateTime(anyInt())).thenReturn("yyyy-MM-dd HH:mm:ss");
        lenient().when(businessUnitRepository.getFormatoDate(anyInt())).thenReturn("yyyy-MM-dd");
        lenient().when(containerRestrictionRepository.findRestrictionsByContainerId(anyInt(), anyInt(), anyString())).thenReturn(Collections.emptyList());
        lenient().when(conteconLogRepository.findTopByEirIdAndConditions(anyInt())).thenReturn(idPreGateReception);
        lenient().when(catalogLanguageRepository.getCheckRevisionLight(anyString(), anyInt())).thenReturn(Collections.singletonList("Check"));
        lenient().when(eirActivityZoneRepository.findEirActivityZoneByEirId(anyInt(), anyInt(), anyInt(), anyString())).thenReturn(Collections.emptyList());
        lenient().when(eirZoneRepository.findEirZoneByEirId(anyInt(), anyInt(), anyInt(), anyString())).thenReturn(Collections.emptyList());
        lenient().when(eirObservationInspectorRepository.findObservationsByEirIdAndType(anyInt(), anyInt(), anyInt())).thenReturn(Collections.emptyList());
        lenient().when(gateTransmissionRepository.findByEirId(anyInt(), anyInt(), anyInt())).thenReturn(Collections.emptyList());
    }

    @Test
    void testGenerateEirMainOutputShouldReturnEmptyListWhenNoData() {
        List<EirGetOutput.EirMainData> result = eirGetService.generateEirMainOutput(
                eirId, languageId, containerDummyId, subBusinessUnitId, isFull, idPreGateReception, Collections.emptyList(), Collections.emptyList());

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGenerateEirMainOutputShouldMapDataCorrectly() {
        EirDataProjection mockData = mock(EirDataProjection.class);
        when(mockData.getEirId()).thenReturn(eirId);
        when(mockData.getTipoGate()).thenReturn("Gate Type");
        when(mockData.getNumeroContenedor()).thenReturn("Container123");

        when(eirRepository.findEirData(anyInt(), anyInt(), anyInt(), any(), any(), anyInt(), anyInt(), anyInt()))
                .thenReturn(Collections.singletonList(mockData));

        List<EirGetOutput.EirMainData> result = eirGetService.generateEirMainOutput(
                eirId, languageId, containerDummyId, subBusinessUnitId, isFull, idPreGateReception, Collections.emptyList(), Collections.emptyList());

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Gate Type", result.getFirst().getTipoGate());
        assertEquals("Container123", result.getFirst().getNumeroContenedor());
    }

    @Test
    void testGenerateEirMainOutputShouldSetDocumentDetails() {
        EirDataProjection mockData = mock(EirDataProjection.class);
        when(mockData.getEirId()).thenReturn(eirId);

        DocumentDTO doc1 = new DocumentDTO("Invoice", "INV123", "Maersk");
        DocumentDTO doc2 = new DocumentDTO("Invoice", "INV124", "Maersk");
        List<DocumentDTO> docs = Arrays.asList(doc1, doc2);

        when(eirRepository.findEirData(anyInt(), anyInt(), anyInt(), any(), any(), anyInt(), anyInt(), anyInt()))
                .thenReturn(Collections.singletonList(mockData));

        List<EirGetOutput.EirMainData> result = eirGetService.generateEirMainOutput(
                eirId, languageId, containerDummyId, subBusinessUnitId, isFull, idPreGateReception, docs, Collections.emptyList());

        assertNotNull(result);
        assertEquals("Maersk Invoice, Invoice", result.getFirst().getDocumentoTipo());
        assertEquals("Invoice", result.getFirst().getDocumento());
    }

    @Test
    void testGetEirDataShouldSetEirActivityZones() {
        Integer testEirId = 1;
        Integer testLanguageId = 1;
        Integer testSubBusinessUnitId = 1;
        String formatDateTime = "yyyy-MM-dd HH:mm:ss";

        BusinessUnit mockBusinessUnit = mock(BusinessUnit.class);
        when(mockBusinessUnit.getId()).thenReturn(10);

        Container mockContainer = mock(Container.class);
        when(mockContainer.getId()).thenReturn(20);

        Eir mockEir = mock(Eir.class);
        when(mockEir.getChecksRevisionLight()).thenReturn("Check123");
        when(mockEir.getBusinessUnit()).thenReturn(mockBusinessUnit);
        when(mockEir.getContainer()).thenReturn(mockContainer);
        when(eirRepository.findById(eirId)).thenReturn(Optional.of(mockEir));

        when(containerRepository.findContainerDummyId()).thenReturn(100);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS)).thenReturn(200);

        EirActivityZoneProjection mockProjection = mock(EirActivityZoneProjection.class);
        when(mockProjection.getActividad()).thenReturn("Activity1");
        when(mockProjection.getConcluido()).thenReturn("Yes");
        when(mockProjection.getEstructuraDanada()).thenReturn("No");
        when(mockProjection.getMaquinariaDanada()).thenReturn("No");
        when(mockProjection.getZonaResultado()).thenReturn("ZoneA");
        when(mockProjection.getFInicio()).thenReturn("2025-01-01 10:00:00");
        when(mockProjection.getFTermino()).thenReturn("2025-01-01 12:00:00");
        when(mockProjection.getSensor()).thenReturn("Sensor1");

        List<EirActivityZoneProjection> mockProjections = List.of(mockProjection);
        when(eirActivityZoneRepository.findEirActivityZoneByEirId(testEirId, testLanguageId, testSubBusinessUnitId, formatDateTime))
                .thenReturn(mockProjections);

        EirGetOutput output = eirGetService.getEirData(testSubBusinessUnitId, testEirId, testSubBusinessUnitId);

        assertNotNull(output, "EirGetOutput should not be null");
        assertEquals(1, output.getActivities().size());
        assertEquals("Activity1", output.getActivities().getFirst().getActividad());

        verify(eirRepository, times(1)).findById(eirId);
        verify(containerRepository, times(1)).findContainerDummyId();
        verify(catalogRepository, times(1)).findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS);
        verify(eirActivityZoneRepository, times(1))
                .findEirActivityZoneByEirId(eirId, languageId, subBusinessUnitId, formatDateTime);
    }

    @Test
    void testGetEirDataShouldSetObservations() {
        Integer testEirId = 1;
        Integer testLanguageId = 1;
        Integer testSubBusinessUnitId = 1;

        Eir mockEir = mock(Eir.class);
        when(eirRepository.findById(testEirId)).thenReturn(Optional.of(mockEir));

        Container mockContainer = mock(Container.class);
        when(mockContainer.getId()).thenReturn(100);
        when(mockEir.getContainer()).thenReturn(mockContainer);

        BusinessUnit mockBusinessUnit = mock(BusinessUnit.class);
        when(mockBusinessUnit.getId()).thenReturn(200);
        when(mockEir.getBusinessUnit()).thenReturn(mockBusinessUnit);

        EirObservationInspectorProjection mockObservationSeco = mock(EirObservationInspectorProjection.class);
        when(mockObservationSeco.getRow()).thenReturn("1");
        when(mockObservationSeco.getObservacionDescripcion()).thenReturn("Observation Seco");

        List<EirObservationInspectorProjection> mockObservationsSecos = List.of(mockObservationSeco);
        when(eirObservationInspectorRepository.findObservationsByEirIdAndType(testEirId, testLanguageId, 0))
                .thenReturn(mockObservationsSecos);

        EirObservationInspectorProjection mockObservationReefer = mock(EirObservationInspectorProjection.class);
        when(mockObservationReefer.getRow()).thenReturn("2");
        when(mockObservationReefer.getObservacionDescripcion()).thenReturn("Observation Reefer");

        List<EirObservationInspectorProjection> mockObservationsReefer = List.of(mockObservationReefer);
        when(eirObservationInspectorRepository.findObservationsByEirIdAndType(testEirId, testLanguageId, 1))
                .thenReturn(mockObservationsReefer);

        EirGetOutput output = eirGetService.getEirData(testSubBusinessUnitId, testEirId, testLanguageId);

        assertNotNull(output, "EirGetOutput should not be null");

        assertNotNull(output.getObservacionesInspectorSecos(), "ObservacionesInspectorSecos should not be null");
        assertEquals(1, output.getObservacionesInspectorSecos().size());
        assertEquals("1", output.getObservacionesInspectorSecos().getFirst().getRow());
        assertEquals("Observation Seco", output.getObservacionesInspectorSecos().getFirst().getObservationDescription());

        assertNotNull(output.getObservacionesInspectorReefer(), "ObservacionesInspectorReefer should not be null");
        assertEquals(1, output.getObservacionesInspectorReefer().size());
        assertEquals("2", output.getObservacionesInspectorReefer().getFirst().getRow());
        assertEquals("Observation Reefer", output.getObservacionesInspectorReefer().getFirst().getObservationDescription());

        verify(eirRepository, times(1)).findById(testEirId);
        verify(eirObservationInspectorRepository, times(1)).findObservationsByEirIdAndType(eirId, languageId, 0);
        verify(eirObservationInspectorRepository, times(1)).findObservationsByEirIdAndType(eirId, languageId, 1);
    }

    @Test
    void testGetEirDataShouldSetEdiCodecoList() {
        Integer testEirId = 1;
        Integer testLanguageId = 1;
        Integer testSubBusinessUnitId = 1;

        Eir mockEir = mock(Eir.class);
        when(eirRepository.findById(testEirId)).thenReturn(Optional.of(mockEir));

        Container mockContainer = mock(Container.class);
        when(mockContainer.getId()).thenReturn(100);
        when(mockEir.getContainer()).thenReturn(mockContainer);

        BusinessUnit mockBusinessUnit = mock(BusinessUnit.class);
        when(mockBusinessUnit.getId()).thenReturn(200);
        when(mockEir.getBusinessUnit()).thenReturn(mockBusinessUnit);

        EdiCodecoProjection mockEdiCodeco = mock(EdiCodecoProjection.class);
        when(mockEdiCodeco.getEirId()).thenReturn(testEirId);
        when(mockEdiCodeco.getIdTransmision()).thenReturn(123);
        when(mockEdiCodeco.getEstadoTransmision()).thenReturn("Success");
        when(mockEdiCodeco.getSistemaEntrega()).thenReturn("SystemA");
        when(mockEdiCodeco.getInOut()).thenReturn("IN");
        when(mockEdiCodeco.getEmptyFull()).thenReturn("Empty");
        when(mockEdiCodeco.getLineaDo()).thenReturn("LineA");
        when(mockEdiCodeco.getFechaActividad()).thenReturn(String.valueOf(LocalDateTime.now()));
        when(mockEdiCodeco.getNumeroContenedor()).thenReturn("CONT12345");
        when(mockEdiCodeco.getCodigoIsoContenedor()).thenReturn("ISO123");
        when(mockEdiCodeco.getTaraContenedor()).thenReturn(5000);
        when(mockEdiCodeco.getDocumento()).thenReturn("DOC123");
        when(mockEdiCodeco.getNaveViaje()).thenReturn("Ship123");
        when(mockEdiCodeco.getOperacion()).thenReturn("LOAD");
        when(mockEdiCodeco.getPesoMercaderia()).thenReturn(10000.0);
        when(mockEdiCodeco.getClienteRazonSocial()).thenReturn("ClientXYZ");
        when(mockEdiCodeco.getPrecinto1()).thenReturn("Seal1");
        when(mockEdiCodeco.getPrecinto2()).thenReturn("Seal2");
        when(mockEdiCodeco.getPrecinto3()).thenReturn("Seal3");
        when(mockEdiCodeco.getPrecinto4()).thenReturn("Seal4");
        when(mockEdiCodeco.getManifiesto()).thenReturn("ManifestXYZ");
        when(mockEdiCodeco.getSituacionCaja()).thenReturn("Good");
        when(mockEdiCodeco.getSituacionMaquina()).thenReturn("Operational");
        when(mockEdiCodeco.getPuertoDescarga()).thenReturn("Port A");
        when(mockEdiCodeco.getPuertoEmbarque()).thenReturn("Port B");
        when(mockEdiCodeco.getVehiculoPlaca()).thenReturn("Plate123");
        when(mockEdiCodeco.getTipoMovimiento()).thenReturn("Import");
        when(mockEdiCodeco.getTransaccion()).thenReturn("Txn123");
        when(mockEdiCodeco.getFechaRegistro()).thenReturn(String.valueOf(LocalDateTime.now()));
        when(mockEdiCodeco.getEstadoArchivo()).thenReturn("Processed");
        when(mockEdiCodeco.getFechaEnvio()).thenReturn(String.valueOf(LocalDateTime.now()));
        when(mockEdiCodeco.getCanalEnvio()).thenReturn("Email");
        when(mockEdiCodeco.getNombreArchivo()).thenReturn("File123.txt");
        when(mockEdiCodeco.getReenvio()).thenReturn("Yes");
        when(mockEdiCodeco.getUsuarioReenvio()).thenReturn("User123");
        when(mockEdiCodeco.getOrigenCreacionEir()).thenReturn("SystemX");
        when(mockEdiCodeco.getLocal()).thenReturn("Warehouse A");
        when(mockEdiCodeco.getEdiCodecoComentario()).thenReturn("All good");
        when(mockEdiCodeco.getSeteoEdiCodecoId()).thenReturn(999);
        when(mockEdiCodeco.getActivo()).thenReturn("Yes");
        when(mockEdiCodeco.getContenidoArchivoCodeco()).thenReturn("ContentData");

        List<EdiCodecoProjection> mockEdiCodecos = List.of(mockEdiCodeco);
        when(gateTransmissionRepository.findByEirId(testEirId, testSubBusinessUnitId, testLanguageId)).thenReturn(mockEdiCodecos);

        EirGetOutput output = eirGetService.getEirData(subBusinessUnitId, eirId, languageId);

        assertNotNull(output, "EirGetOutput should not be null");
        assertNotNull(output.getEdiCodecoList(), "EdiCodeco list should not be null");
        assertEquals(1, output.getEdiCodecoList().size());

        EirGetOutput.EirEdiCodecoData ediCodecoData = output.getEdiCodecoList().getFirst();
        assertEquals(eirId, ediCodecoData.getEirId());
        assertEquals(123, ediCodecoData.getIdTransmision());
        assertEquals("Success", ediCodecoData.getEstadoTransmision());
        assertEquals("SystemA", ediCodecoData.getSistemaEntrega());
        assertEquals("IN", ediCodecoData.getInOut());
        assertEquals("Empty", ediCodecoData.getEmptyFull());
        assertEquals("LineA", ediCodecoData.getLineaDo());
        assertNotNull(ediCodecoData.getFechaActividad());
        assertEquals("CONT12345", ediCodecoData.getNumeroContenedor());
        assertEquals("ISO123", ediCodecoData.getCodigoIsoContenedor());
        assertEquals(5000, ediCodecoData.getTaraContenedor());
        assertEquals("DOC123", ediCodecoData.getDocumento());
        assertEquals("Ship123", ediCodecoData.getNaveViaje());
        assertEquals("LOAD", ediCodecoData.getOperacion());
        assertEquals(10000, ediCodecoData.getPesoMercaderia());
        assertEquals("ClientXYZ", ediCodecoData.getClienteRazonSocial());
        assertEquals("Seal1", ediCodecoData.getPrecinto1());
        assertEquals("Seal2", ediCodecoData.getPrecinto2());
        assertEquals("Seal3", ediCodecoData.getPrecinto3());
        assertEquals("Seal4", ediCodecoData.getPrecinto4());
        assertEquals("ManifestXYZ", ediCodecoData.getManifiesto());
        assertEquals("Good", ediCodecoData.getSituacionCaja());
        assertEquals("Operational", ediCodecoData.getSituacionMaquina());
        assertEquals("Port A", ediCodecoData.getPuertoDescarga());
        assertEquals("Port B", ediCodecoData.getPuertoEmbarque());
        assertEquals("Plate123", ediCodecoData.getVehiculoPlaca());
        assertEquals("Import", ediCodecoData.getTipoMovimiento());
        assertEquals("Txn123", ediCodecoData.getTransaccion());
        assertNotNull(ediCodecoData.getFechaRegistro());
        assertEquals("Processed", ediCodecoData.getEstadoArchivo());
        assertNotNull(ediCodecoData.getFechaEnvio());
        assertEquals("Email", ediCodecoData.getCanalEnvio());
        assertEquals("File123.txt", ediCodecoData.getNombreArchivo());
        assertEquals("Yes", ediCodecoData.getReenvio());
        assertEquals("User123", ediCodecoData.getUsuarioReenvio());
        assertEquals("SystemX", ediCodecoData.getOrigenCreacionEir());
        assertEquals("Warehouse A", ediCodecoData.getLocal());
        assertEquals("All good", ediCodecoData.getComentarioEdi());
        assertEquals(999, ediCodecoData.getSeteoEdiCodecoId());
        assertEquals("Yes", ediCodecoData.getActivo());
        assertEquals("ContentData", ediCodecoData.getContenidoArchivoCodeco());

        verify(eirRepository, times(1)).findById(eirId);
        verify(gateTransmissionRepository, times(1)).findByEirId(eirId, subBusinessUnitId, languageId);
    }

    @Test
    void testGetEirDataShouldSetDriverPhotos() {
        Integer testEirId = 1;
        Integer testSubBusinessUnitId = 1;
        Integer testLanguageId = 1;

        Eir mockEir = mock(Eir.class);
        when(eirRepository.findById(testEirId)).thenReturn(Optional.of(mockEir));

        BusinessUnit mockBusinessUnit = mock(BusinessUnit.class);
        when(mockEir.getBusinessUnit()).thenReturn(mockBusinessUnit);
        when(mockBusinessUnit.getId()).thenReturn(testSubBusinessUnitId);

        Container mockContainer = mock(Container.class);
        when(mockEir.getContainer()).thenReturn(mockContainer);
        when(mockContainer.getId()).thenReturn(999);

        EirGateDriverPhotoProjection mockDriverPhoto = mock(EirGateDriverPhotoProjection.class);
        when(mockDriverPhoto.getEirId()).thenReturn(eirId);
        when(mockDriverPhoto.getEirGateDriverPhotoId()).thenReturn(101);
        when(mockDriverPhoto.getAdjuntoId()).thenReturn(202);
        when(mockDriverPhoto.getId()).thenReturn(303);
        when(mockDriverPhoto.getUrl()).thenReturn("http://example.com/photo.jpg");

        List<EirGateDriverPhotoProjection> mockDriverPhotos = List.of(mockDriverPhoto);
        when(eirRepository.findDriverPhotosByEirId(eirId)).thenReturn(mockDriverPhotos);

        EirGetOutput output = eirGetService.getEirData(testSubBusinessUnitId, testEirId, testLanguageId);

        assertNotNull(output, "EirGetOutput should not be null");
        assertNotNull(output.getDriverPhotos(), "DriverPhotos list should not be null");
        assertFalse(output.getDriverPhotos().isEmpty(), "DriverPhotos list should not be empty");

        EirGetOutput.EirGateDriverPhotoDTO driverPhotoData = output.getDriverPhotos().getFirst();
        assertEquals(testEirId, driverPhotoData.getEirId());
        assertEquals(101, driverPhotoData.getEirGateDriverPhotoId());
        assertEquals(202, driverPhotoData.getAdjuntoId());
        assertEquals(303, driverPhotoData.getId());
        assertEquals("http://example.com/photo.jpg", driverPhotoData.getUrl());

        verify(eirRepository, times(1)).findById(testEirId);
        verify(eirRepository, times(1)).findDriverPhotosByEirId(testEirId);
    }
}