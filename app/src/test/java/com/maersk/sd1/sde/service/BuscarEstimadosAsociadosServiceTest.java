package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.repository.CatalogLanguageRepository;
import com.maersk.sd1.common.repository.EstimateEmrRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sde.dto.BuscarEstimadosAsociadosInput;
import com.maersk.sd1.sde.dto.BuscarEstimadosAsociadosOutput;
import com.maersk.sd1.common.model.EstimateEmr;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BuscarEstimadosAsociadosServiceTest {

    @Mock
    private EstimateEmrRepository estimateEmrRepository;

    @Mock
    private CatalogLanguageRepository catalogLanguageRepository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    @InjectMocks
    private BuscarEstimadosAsociadosService buscarEstimadosAsociadosService;

    private BuscarEstimadosAsociadosInput.Input input;

    @BeforeEach
    void setUp() {
        input = new BuscarEstimadosAsociadosInput.Input();
        input.setEirId(1);
        input.setLanguageId(1);
    }

    @Test
    void buscarEstimadosAsociados_withValidInput_returnsExpectedOutput() {
        // Create mock for EstimateEmr
        EstimateEmr emr = new EstimateEmr();
        emr.setId(1);
        emr.setEstimateDateInspection(LocalDateTime.now());

        emr.setCatEstimateType(new Catalog());
        emr.getCatEstimateType().setId(1);

        emr.setCatEstimateStatus(new Catalog());
        emr.getCatEstimateStatus().setId(1);

        // Mock the repository methods
        when(estimateEmrRepository.findByEirIdAndActiveTrueOrderByEstimateDateInspectionAsc(input.getEirId()))
                .thenReturn(List.of(emr));
        when(catalogLanguageRepository.fnCatalogoTraducidoDes(1, input.getLanguageId()))
                .thenReturn("Translated Type");
        when(catalogLanguageRepository.fnCatalogoTraducidoDes(1, input.getLanguageId()))
                .thenReturn("Translated Status");

        BuscarEstimadosAsociadosOutput output = buscarEstimadosAsociadosService.buscarEstimadosAsociados(input);
        assertEquals(1, output.getDetails().size());
    }


    @Test
    void buscarEstimadosAsociados_withNoEstimates_returnsEmptyOutput() {
        when(estimateEmrRepository.findByEirIdAndActiveTrueOrderByEstimateDateInspectionAsc(input.getEirId()))
                .thenReturn(Collections.emptyList());

        BuscarEstimadosAsociadosOutput output = buscarEstimadosAsociadosService.buscarEstimadosAsociados(input);

        assertEquals(0, output.getDetails().size());
    }

    @Test
    void buscarEstimadosAsociados_withException_returnsEmptyOutput() {
        when(estimateEmrRepository.findByEirIdAndActiveTrueOrderByEstimateDateInspectionAsc(input.getEirId()))
                .thenThrow(new RuntimeException("Database error"));

        BuscarEstimadosAsociadosOutput output = buscarEstimadosAsociadosService.buscarEstimadosAsociados(input);

        assertEquals(0, output.getDetails().size());
    }
}