package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.MercplusComponentListInput;
import com.maersk.sd1.sde.dto.MercplusComponentListOutput;
import com.maersk.sd1.sde.service.MercplusComponentListService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

class MercplusComponentListControllerTest {

    @Mock
    private MercplusComponentListService mercplusComponentListService;

    @InjectMocks
    private MercplusComponentListController mercplusComponentListController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testMercplusComponentList() {
        // Arrange
        MercplusComponentListInput.Root request = new MercplusComponentListInput.Root();
        List<MercplusComponentListOutput> mockOutput = Collections.singletonList(new MercplusComponentListOutput());
        when(mercplusComponentListService.fetchMercplusComponents()).thenReturn(mockOutput);

        // Act
        ResponseEntity<ResponseController<List<MercplusComponentListOutput>>> response = mercplusComponentListController.mercplusComponentList(request);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(mockOutput, response.getBody().getResult());
    }

    @Test
    void testMercplusComponentList_Exception() {
        // Arrange
        MercplusComponentListInput.Root request = new MercplusComponentListInput.Root();
        when(mercplusComponentListService.fetchMercplusComponents()).thenThrow(new RuntimeException("Service error"));

        // Act
        ResponseEntity<ResponseController<List<MercplusComponentListOutput>>> response = mercplusComponentListController.mercplusComponentList(request);

        // Assert
        assertEquals(500, response.getStatusCode().value());
        assertEquals("Service error", response.getBody().getMessage());
    }
}