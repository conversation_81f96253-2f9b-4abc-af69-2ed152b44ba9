package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.EdiCodecoMailInputDto;
import com.maersk.sd1.sde.dto.EdiCodecoMailOutputDto;
import com.maersk.sd1.sde.service.EdiCodecoMailService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EdiCodecoMailControllerTest {

    @InjectMocks
    private EdiCodecoMailController ediCodecoMailController;

    @Mock
    private EdiCodecoMailService ediCodecoMailService;

    private EdiCodecoMailInputDto.Root request;
    private EdiCodecoMailOutputDto outputDto;

    @BeforeEach
    void setUp() {
        request = new EdiCodecoMailInputDto.Root();
        EdiCodecoMailInputDto.Prefix prefix = new EdiCodecoMailInputDto.Prefix();
        EdiCodecoMailInputDto.Input input = new EdiCodecoMailInputDto.Input();
        input.setEdiCodecoEnvioCorreoId(1);
        input.setSeteoEdiCodecoId(1);
        input.setTipoEstructuraId(1);
        input.setNombreArchivo("testFile");
        prefix.setInput(input);
        request.setPrefix(prefix);

        outputDto = new EdiCodecoMailOutputDto();
        outputDto.setRespEstado(1);
        outputDto.setRespMensaje("Success");
        outputDto.setRespCorreo("<EMAIL>");
    }

    @Test
    void testSendEdiCodecoMailSuccess() {
        when(ediCodecoMailService.sendEdiCodecoMail(any(EdiCodecoMailInputDto.Input.class))).thenReturn(outputDto);

        ResponseEntity<ResponseController<EdiCodecoMailOutputDto>> response = ediCodecoMailController.sendEdiCodecoMail(request);

        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        assertEquals("Success", response.getBody().getResult().getRespMensaje());
        assertEquals("<EMAIL>", response.getBody().getResult().getRespCorreo());
    }

    @Test
    void testSendEdiCodecoMailInvalidRequest() {
        request.setPrefix(null);

        ResponseEntity<ResponseController<EdiCodecoMailOutputDto>> response = ediCodecoMailController.sendEdiCodecoMail(request);

        assertEquals(500, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("Invalid request. Prefix or input is null.", response.getBody().getResult().getRespMensaje());
        assertNull(response.getBody().getResult().getRespCorreo());
    }

    @Test
    void testSendEdiCodecoMailException() {
        when(ediCodecoMailService.sendEdiCodecoMail(any(EdiCodecoMailInputDto.Input.class))).thenThrow(new RuntimeException("Exception"));

        ResponseEntity<ResponseController<EdiCodecoMailOutputDto>> response = ediCodecoMailController.sendEdiCodecoMail(request);

        assertEquals(500, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("Exception", response.getBody().getResult().getRespMensaje());
        assertNull(response.getBody().getResult().getRespCorreo());
    }
}