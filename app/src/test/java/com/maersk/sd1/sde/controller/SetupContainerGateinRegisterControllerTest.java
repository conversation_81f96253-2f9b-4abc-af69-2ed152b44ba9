package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.SetupContainerGateinRegisterInput;
import com.maersk.sd1.sde.dto.SetupContainerGateinRegisterOutput;
import com.maersk.sd1.sde.service.SetupContainerGateinRegisterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SetupContainerGateinRegisterControllerTest {

    @Mock
    private SetupContainerGateinRegisterService seteoEdiCodecoRegisterService;

    @InjectMocks
    private SetupContainerGateinRegisterController seteoEdiCodecoRegisterController;

    private SetupContainerGateinRegisterInput.Root request;
    private SetupContainerGateinRegisterInput.Input input;

    @BeforeEach
    void setUp() {
        input = new SetupContainerGateinRegisterInput.Input();
        input.setUnidadNegocioId(1L);
        input.setSubUnidadNegocioId(1L);
        input.setLineaNavieraId(4105);
        input.setSistemaEntrega("SistemaY");
        input.setInfoSistemaEntrega("Updated delivery system info");
        input.setIdentificadorReceptor("REC456");
        input.setEnviarGateInEmpty(false);
        input.setEnviarGateOutEmpty(true);
        input.setEnviarGateInFull(false);
        input.setEnviarGateOutFull(true);
        input.setEnviarStatusActivity(false);
        input.setCatFormatoGateOutEmpty(1L);
        input.setCatFormatoGateInFull(1L);
        input.setCatFormatoGateOutFull(1L);
        input.setCatFormatoGateInEmpty(1L);
        input.setCatFormatoStatusActivity(1L);
        input.setCatCanalEnvioId(1L);
        input.setCatModoGenerarArchivoId(1L);
        input.setCorreoCodecoDestino("<EMAIL>");
        input.setCorreoTelexDestino("<EMAIL>");
        input.setParametro1("Q1");
        input.setParametro2("Q2");
        input.setParametro3("Q3");
        input.setParametro4("Q4");
        input.setEsHistorico(true);
        input.setFechaDebaja("2026-06-30");
        input.setMotivoDebaja("Updated reason for deactivation");
        input.setActivo(false);
        input.setUsuarioRegistroId(2L);
        input.setParametro5("Q5");
        input.setParametro6("Q6");
        input.setAzureIdCodeco("AZURE_CODECO_789");
        input.setAzureIdTelex("AZURE_TELEX_987");
        input.setSftpId("SFTP_654");
        input.setExtensionArchivoEnviar(".json");
        input.setMinutosTranscurridos(45);
        input.setSubUnidadesJson(Arrays.asList(
                Map.of("subUnidad", "updatedExample1"),
                Map.of("subUnidad", "updatedExample2")
        ));
        input.setGateInEmptyProcedenciaJson(Collections.singletonList(
                Map.of("catalogo_id", 1)
        ));
        input.setGateOutEmptyProcedenciaJson(Collections.singletonList(
                Map.of("catalogo_id", 2)
        ));

        request = new SetupContainerGateinRegisterInput.Root();
        SetupContainerGateinRegisterInput.Prefix prefix = new SetupContainerGateinRegisterInput.Prefix();
        prefix.setInput(input);
        request.setPrefix(prefix);
    }

    @Test
    void Given_ValidRequest_When_Register_Then_ReturnSuccessResponse() {
        SetupContainerGateinRegisterOutput output = new SetupContainerGateinRegisterOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Successfully register");
        output.setRespNewId(1);

        when(seteoEdiCodecoRegisterService.registerCodecoSetting(any(SetupContainerGateinRegisterInput.Input.class)))
                .thenReturn(output);

        ResponseEntity<ResponseController<SetupContainerGateinRegisterOutput>> response = seteoEdiCodecoRegisterController.register(request);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        assertEquals("Successfully register", response.getBody().getResult().getRespMensaje());
            assertNotNull(response.getBody().getResult().getRespNewId());
    }

    @Test
    void Given_ServiceThrowsException_When_Register_Then_ReturnErrorResponse() {
        when(seteoEdiCodecoRegisterService.registerCodecoSetting(any(SetupContainerGateinRegisterInput.Input.class)))
                .thenThrow(new RuntimeException("Service error"));

        ResponseEntity<ResponseController<SetupContainerGateinRegisterOutput>> response = seteoEdiCodecoRegisterController.register(request);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("Service error", response.getBody().getResult().getRespMensaje());
    }
}