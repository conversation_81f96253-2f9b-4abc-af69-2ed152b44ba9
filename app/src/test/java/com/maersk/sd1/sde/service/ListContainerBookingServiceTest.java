package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.sde.dto.ListContainerBookingInput;
import com.maersk.sd1.sde.dto.ListContainerBookingOutput;
import com.maersk.sd1.common.repository.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ListContainerBookingServiceTest {

    @Mock
    private ContainerPreassignmentRepository containerPreassignmentRepository;

    @Mock
    private ContainerRestrictionRepository containerRestrictionRepository;

    @Mock
    private ContainerRestrictionDetailRepository containerRestrictionDetailRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @InjectMocks
    private ListContainerBookingService listContainerBookingService;

    private ListContainerBookingInput.Input input;

    @BeforeEach
    void setUp() {
        input = new ListContainerBookingInput.Input();
        input.setUnidadNegocioId(1);
        input.setBookingDetalleId(10);
        input.setSubUnidadNegocioId(100);
    }

    @Test
    void testListContainerBookingSuccess() {
        Catalog mockIsEmptyCatalog = new Catalog();
        mockIsEmptyCatalog.setId(43083);
        when(catalogRepository.findByAlias("43083")).thenReturn(mockIsEmptyCatalog);

        ContainerPreassignment cp = new ContainerPreassignment();
        cp.setId(1);
        cp.setActive(true);
        cp.setPreassignmentDate(LocalDateTime.now());
        cp.setRegistrationDate(LocalDateTime.now());
        Container container = new Container();
        container.setId(5);
        container.setContainerNumber("ABC1234567");
        container.setMaximunPayload(30000);
        Catalog gradeCatalog = new Catalog();
        gradeCatalog.setDescription("ClaseX");
        container.setCatGrade(gradeCatalog);
        ShippingLine shippingLine = new ShippingLine();
        shippingLine.setShippingLineCompany("MAEU");
        container.setShippingLine(shippingLine);
        cp.setContainer(container);
        User regUser = new User();
        regUser.setId(22);
        regUser.setNames("John");
        regUser.setFirstLastName("Doe");
        cp.setRegistrationUser(regUser);

        when(containerPreassignmentRepository.findByBookingDetailIdAndActiveOrderByRegistrationDateAsc(10, true))
                .thenReturn(Collections.singletonList(cp));

        when(containerRestrictionRepository.findByContainerIdAndSubBusinessUnitIdAndCatEmptyFullIdAndReleasedRestrictionAndActive(
                5, 100, 43083, false, true)).thenReturn(new ArrayList<>());

        ListContainerBookingOutput result = listContainerBookingService.listContainerBooking(input);
        assertNotNull(result);
        assertEquals(1, result.getRespEstado());
        assertNotNull(result.getContainers());
        assertFalse(result.getContainers().isEmpty());
        assertEquals("ABC1234567", result.getContainers().getFirst().getNumeroContenedor());
        assertEquals("ClaseX", result.getContainers().getFirst().getClase());
        assertEquals("MAEU", result.getContainers().getFirst().getLineaNaviera());
    }

    @Test
    void testListContainerBookingWithRestriction() {
        Catalog mockIsEmptyCatalog = new Catalog();
        mockIsEmptyCatalog.setId(43083);
        when(catalogRepository.findByAlias("43083")).thenReturn(mockIsEmptyCatalog);

        ContainerPreassignment cp = new ContainerPreassignment();
        cp.setId(2);
        cp.setActive(true);
        cp.setPreassignmentDate(LocalDateTime.now());
        cp.setRegistrationDate(LocalDateTime.now());
        Container container = new Container();
        container.setId(5);
        container.setContainerNumber("XYZ1234567");
        container.setMaximunPayload(40000);
        cp.setContainer(container);
        when(containerPreassignmentRepository.findByBookingDetailIdAndActiveOrderByRegistrationDateAsc(10, true))
                .thenReturn(Collections.singletonList(cp));

        ContainerRestriction restriction = new ContainerRestriction();
        restriction.setId(1000);
        restriction.setRestrictionAnnotation(" - special note");
        ContainerRestrictionDetail detail = new ContainerRestrictionDetail();
        Catalog reasonCatalog = new Catalog();
        reasonCatalog.setDescription("Reason1");
        detail.setCatRestrictionReason(reasonCatalog);
        detail.setActive(true);
        when(containerRestrictionRepository.findByContainerIdAndSubBusinessUnitIdAndCatEmptyFullIdAndReleasedRestrictionAndActive(5, 100, 43083, false, true))
                .thenReturn(Collections.singletonList(restriction));
        when(containerRestrictionDetailRepository.findByContainerRestrictionIdAndActive(1000, true))
                .thenReturn(Collections.singletonList(detail));

        ListContainerBookingOutput result = listContainerBookingService.listContainerBooking(input);
        assertNotNull(result);
        assertEquals(1, result.getRespEstado());
        assertFalse(result.getContainers().isEmpty());
        assertEquals("XYZ1234567", result.getContainers().getFirst().getNumeroContenedor());
        assertEquals("Reason1 - special note", result.getContainers().getFirst().getRestriccionVigente());
    }

    @Test
    void testListContainerBookingException() {
        when(catalogRepository.findByAlias("43083")).thenThrow(new RuntimeException("DB error"));
        ListContainerBookingOutput result = listContainerBookingService.listContainerBooking(input);
        assertEquals(0, result.getRespEstado());
        assertTrue(result.getRespMensaje().contains("DB error"));
    }
}