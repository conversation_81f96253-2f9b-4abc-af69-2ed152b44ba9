package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.repository.CedexMercRepository;
import com.maersk.sd1.sde.dto.CedexSetupCorrectOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CedexSetupCorrectServiceTest {

    @Mock
    private CedexMercRepository cedexMercRepository;

    @InjectMocks
    private CedexSetupCorrectService cedexSetupCorrectService;

    private Map<String, Object> mockResult;

    @BeforeEach
    public void setUp() {
        mockResult = new HashMap<>();
        mockResult.put("@resp_status", 1);
        mockResult.put("@resp_message", "Processed successfully");
    }

    @Test
    void testProcessCedexSetupCorrect_Success() {
        when(cedexMercRepository.callCedexSetupCorrect(
                new BigDecimal("1"),
                "A",
                1,
                new BigDecimal("1"),
                new BigDecimal("1"),
                "Setup"
        )).thenReturn(mockResult);

        CedexSetupCorrectOutput output = cedexSetupCorrectService.processCedexSetupCorrect(
                new BigDecimal("1"),
                "A",
                1,
                new BigDecimal("1"),
                new BigDecimal("1"),
                "Setup"
        );

        assertEquals(1, output.getRespStatus());
        assertEquals("Processed successfully", output.getRespMessage());
    }

    @Test
    void testProcessCedexSetupCorrect_Exception() {
        when(cedexMercRepository.callCedexSetupCorrect(
                new BigDecimal("1"),
                "A",
                1,
                new BigDecimal("1"),
                new BigDecimal("1"),
                "Setup"
        )).thenThrow(new RuntimeException("Database error"));

        CedexSetupCorrectOutput output = cedexSetupCorrectService.processCedexSetupCorrect(
                new BigDecimal("1"),
                "A",
                1,
                new BigDecimal("1"),
                new BigDecimal("1"),
                "Setup"
        );

        assertEquals(0, output.getRespStatus());
        assertEquals("Database error", output.getRespMessage());
    }
}
