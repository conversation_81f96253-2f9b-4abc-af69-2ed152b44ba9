package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.RegisterActivityZoneCntInput;
import com.maersk.sd1.sde.dto.RegisterActivityZoneCntOutput;
import com.maersk.sd1.sde.service.RegisterActivityZoneCntService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RegisterActivityZoneCntControllerTest {

    @Mock
    private RegisterActivityZoneCntService registerActivityZoneCntService;

    @InjectMocks
    private RegisterActivityZoneCntController registerActivityZoneCntController;

    private RegisterActivityZoneCntInput.Root request;
    private RegisterActivityZoneCntOutput output;

    @BeforeEach
    void setUp() {
        RegisterActivityZoneCntInput.Input input = getInput();

        request = new RegisterActivityZoneCntInput.Root();
        RegisterActivityZoneCntInput.Prefix prefix = new RegisterActivityZoneCntInput.Prefix();
        prefix.setInput(input);
        request.setPrefix(prefix);

        output = new RegisterActivityZoneCntOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Success");
        output.setRespNewId(0);
    }

    private static RegisterActivityZoneCntInput.Input getInput() {
        RegisterActivityZoneCntInput.Input input = new RegisterActivityZoneCntInput.Input();
        input.setBusinessUnitId(1);
        input.setSubBusinessUnitId(1);
        input.setSubBusinessUnitLocalId(1);
        input.setEirId(1);
        input.setContenedor("CONT1234567");
        input.setActividadZonaId(1);
        input.setActividad("ACT");
        input.setSiguienteZona("ZONE");
        input.setRepiteActividad("N");
        input.setPrecintoPreasignado("PRE123");
        input.setUsuarioRegistroId(1);
        input.setLanguageId(1);
        return input;
    }

    @Test
    void testSdgRegistrarActividadZonaCnt_Success() {
        when(registerActivityZoneCntService.registrarActividadZonaCnt(any(RegisterActivityZoneCntInput.Input.class)))
                .thenReturn(output);

        ResponseEntity<ResponseController<RegisterActivityZoneCntOutput>> response = registerActivityZoneCntController.sdgRegistrarActividadZonaCnt(request);

        assertEquals(200, response.getStatusCode().value());
        assertEquals("Success", Objects.requireNonNull(response.getBody()).getResult().getRespMensaje());
    }

    @Test
    void testSdgRegistrarActividadZonaCnt_Exception() {
        when(registerActivityZoneCntService.registrarActividadZonaCnt(any(RegisterActivityZoneCntInput.Input.class)))
                .thenThrow(new RuntimeException("Database error"));

        ResponseEntity<ResponseController<RegisterActivityZoneCntOutput>> response = registerActivityZoneCntController.sdgRegistrarActividadZonaCnt(request);

        assertEquals(500, response.getStatusCode().value());
        assertEquals("Database error", Objects.requireNonNull(response.getBody()).getResult().getRespMensaje());
    }
}