package com.maersk.sd1.sdg.service;

import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.sdg.controller.dto.ResponseTruckDepartureRegisterBeforeYard;
import com.maersk.sd1.sdg.controller.dto.SdgTruckDepartureRegisterInput;
import com.maersk.sd1.sdg.controller.dto.SdgTruckDepartureRegisterOutput;
import com.maersk.sd1.sdg.repository.*;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

class TruckDepartureRegisterServiceTest {


    private final SdgTruckDepartureRegisterInput.Root root = new SdgTruckDepartureRegisterInput.Root();
    private final SdgTruckDepartureRegisterInput.Input input = new SdgTruckDepartureRegisterInput.Input();
    private final SdgTruckDepartureRegisterInput.Prefix prefix = new SdgTruckDepartureRegisterInput.Prefix();

    @Mock
    private EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;
    @Mock
    private EirNotificationRepository eirNotificationRepository;
    @Mock
    private EirMultipleRepository eirMultipleRepository;
    @Mock
    private SdgEirRepository eirRepository;
    @Mock
    private CatalogRepository catalogRepository;
    @Mock
    private ContainerRepository containerRepository;
    @Mock
    private UserRepository userRepository;
    @Mock
    private BusinessUnitRepository businessUnitRepository;
    @Mock
    private SystemRuleRepository systemRuleRepository;
    @Mock
    private SdgEirChassisRepository eirChassisRepository;
    @Mock
    private SdgChassisRepository chassisRepository;
    @Mock
    private MessageLanguageService messageLanguageService;
    @Mock
    private ChassisDocumentDetailRepository chassisDocumentDetailRepository;
    @Mock
    private SdgStockChassisRepository stockChassisRepository;
    @Mock
    private ChassisBookingDocumentRepository chassisBookingDocumentRepository;
    @Mock
    private EirSendAppeirRepository eirSendAppeirRepository;
    @Mock
    private CargoDocumentDetailRepository cargoDocumentDetailRepository;
    @Mock
    private VesselProgrammingContainerRepository vesselProgrammingContainerRepository;
    @Mock
    private AttachmentRepository attachmentRepository;
    @Mock
    private SdfEirPhotoRepository sdfEirPhotoRepository;
    @Mock
    private SdsEirPhotoRepository sdsEirPhotoRepository;
    @Mock
    private SdgStockFullRepository sdgStockFullRepository;
    @Mock
    private SdgStockEmptyRepository sdgStockEmptyRepository;
    @Mock
    private ContainerRestrictionRepository containerRestrictionRepository;
    @Mock
    private FgisInspectionRepository fgisInspectionRepository;
    @Mock
    private ContainerPreassignmentRepository containerPreassignmentRepository;
    @Mock
    private EntityManager entityManager;
    @Mock
    private ApplicationContext context;

    @Spy
    @InjectMocks
    private TruckDepartureRegisterService truckDepartureRegisterService;
    @Spy
    private TruckDepartureRegisterBeforeYardService truckDepartureRegisterBeforeYardServiceSpy;


    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        input.setReject(true);
        input.setEirId(1);
        input.setContainerId(1111);
        input.setSubBusinessUnitLocalId(4);
        input.setUserRegistrationId(1);
        String jsonArray = "[{\"nombre\":\"foto1.jpg\",\"peso\":\"2\",\"formato\":\"jpg\",\"ubicacion\":\"/imagenes/foto1.jpg\",\"url\":\"https://example.com/foto1.jpg\",\"id\":\"12345\",\"tipo_adjunto\":1}," +
                "{\"nombre\":\"foto2.png\",\"peso\":1,\"formato\":\"png\",\"ubicacion\":\"/imagenes/foto2.png\",\"url\":\"https://example.com/foto2.png\",\"id\":\"67890\",\"tipo_adjunto\":2}]";
        input.setPhotos(jsonArray);
        prefix.setInput(input);

        root.setSdg(prefix);
    }

    Eir generateMockEirNoEirChassis() {
        Eir mockEir = new Eir();
        mockEir.setId(900995);

        Container mockContainer = new Container();
        mockContainer.setId(1);
        mockContainer.setContainerNumber("CNT00000043");

        Truck truck = new Truck();
        Person driverPerson = new Person();

        Catalog catMovement = new Catalog();
        catMovement.setId(1);

        Catalog catEmptyFull = new Catalog();
        catEmptyFull.setId(1);

        Catalog catProcedence = new Catalog();
        catProcedence.setId(1);

        Catalog catCreationorigin = new Catalog();
        catCreationorigin.setId(1);

        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setId(1);

        mockEir.setCatMovement(catMovement);
        mockEir.setCatEmptyFull(catEmptyFull);
        mockEir.setContainer(mockContainer);
        mockEir.setTruck(truck);
        mockEir.setDriverPerson(driverPerson);
        mockEir.setCatOrigin(catProcedence);
        mockEir.setBusinessUnit(businessUnit);
        mockEir.setCatCreationOrigin(catCreationorigin);
        VesselProgrammingDetail vesselProgrammingDetail = new VesselProgrammingDetail();
        vesselProgrammingDetail.setId(1);
        mockEir.setVesselProgrammingDetail(vesselProgrammingDetail);
        mockEir.setActive(true);
        mockEir.setControlRevision((short) 1);
        return mockEir;
    }

    Eir generateMockEirWithEirChassis() {
        Eir mockEir = new Eir();
        mockEir.setId(900995);

        Container mockContainer = new Container();
        mockContainer.setId(1);
        mockContainer.setContainerNumber("CNT00000043");

        Truck truck = new Truck();
        Person driverPerson = new Person();

        Catalog catMovement = new Catalog();
        catMovement.setId(999);

        Catalog catEmptyFull = new Catalog();
        catEmptyFull.setId(1);

        Catalog catProcedence = new Catalog();
        catProcedence.setId(1);

        Catalog catCreationorigin = new Catalog();
        catCreationorigin.setId(1);

        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setId(1);

        ChassisDocumentDetail chassisDocumentDetail = new ChassisDocumentDetail();

        EirChassis mockEirChassis = new EirChassis();
        mockEirChassis.setId(1111);
        mockEirChassis.setChassisDocumentDetail(chassisDocumentDetail);
        mockEir.setCatMovement(catMovement);
        mockEir.setCatEmptyFull(catEmptyFull);
        mockEir.setContainer(mockContainer);
        mockEir.setTruck(truck);
        mockEir.setDriverPerson(driverPerson);
        mockEir.setCatOrigin(catProcedence);
        mockEir.setBusinessUnit(businessUnit);
        mockEir.setCatCreationOrigin(catCreationorigin);
        VesselProgrammingDetail vesselProgrammingDetail = new VesselProgrammingDetail();
        vesselProgrammingDetail.setId(1);
        mockEir.setVesselProgrammingDetail(vesselProgrammingDetail);
        mockEir.setActive(true);
        mockEir.setEirChassis(mockEirChassis);
        mockEir.setFlagChassisStayed(false);
        mockEir.setTruckArrivalDate(LocalDateTime.now());
        mockEir.setControlRevision((short) 1);
        Booking booking = new Booking();
        booking.setId(22324);
        mockEir.setBookingGout(booking);
        return mockEir;
    }

    Eir generateMockEirFullWithEirChassis() {
        Eir mockEir = new Eir();
        mockEir.setId(900995);

        Container mockContainer = new Container();
        mockContainer.setId(1);
        mockContainer.setContainerNumber("CNT00000043");

        Truck truck = new Truck();
        Person driverPerson = new Person();

        Catalog catMovement = new Catalog();
        catMovement.setId(999);

        Catalog catEmptyFull = new Catalog();
        catEmptyFull.setId(2);

        Catalog catProcedence = new Catalog();
        catProcedence.setId(1);

        Catalog catCreationorigin = new Catalog();
        catCreationorigin.setId(1);

        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setId(1);

        ChassisDocumentDetail chassisDocumentDetail = new ChassisDocumentDetail();

        EirChassis mockEirChassis = new EirChassis();
        mockEirChassis.setId(1111);
        mockEirChassis.setChassisDocumentDetail(chassisDocumentDetail);
        mockEir.setCatMovement(catMovement);
        mockEir.setCatEmptyFull(catEmptyFull);
        mockEir.setContainer(mockContainer);
        mockEir.setTruck(truck);
        mockEir.setDriverPerson(driverPerson);
        mockEir.setCatOrigin(catProcedence);
        mockEir.setBusinessUnit(businessUnit);
        mockEir.setCatCreationOrigin(catCreationorigin);
        VesselProgrammingDetail vesselProgrammingDetail = new VesselProgrammingDetail();
        vesselProgrammingDetail.setId(1);
        mockEir.setVesselProgrammingDetail(vesselProgrammingDetail);
        mockEir.setActive(true);
        mockEir.setEirChassis(mockEirChassis);
        mockEir.setFlagChassisStayed(false);
        mockEir.setTruckArrivalDate(LocalDateTime.now());

        return mockEir;
    }

    Eir generateMockEirGateOutWithEirChassis() {
        Eir mockEir = new Eir();
        mockEir.setId(900995);

        Container mockContainer = new Container();
        mockContainer.setId(1);
        mockContainer.setContainerNumber("CNT00000043");

        Truck truck = new Truck();
        Person driverPerson = new Person();

        Catalog catMovement = new Catalog();
        catMovement.setId(888);

        Catalog catEmptyFull = new Catalog();
        catEmptyFull.setId(1);

        Catalog catProcedence = new Catalog();
        catProcedence.setId(1);

        Catalog catCreationorigin = new Catalog();
        catCreationorigin.setId(1);

        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setId(1);

        ChassisDocumentDetail chassisDocumentDetail = new ChassisDocumentDetail();

        EirChassis mockEirChassis = new EirChassis();
        mockEirChassis.setId(1111);
        mockEirChassis.setChassisDocumentDetail(chassisDocumentDetail);
        mockEir.setCatMovement(catMovement);
        mockEir.setCatEmptyFull(catEmptyFull);
        mockEir.setContainer(mockContainer);
        mockEir.setTruck(truck);
        mockEir.setDriverPerson(driverPerson);
        mockEir.setCatOrigin(catProcedence);
        mockEir.setBusinessUnit(businessUnit);
        mockEir.setCatCreationOrigin(catCreationorigin);
        VesselProgrammingDetail vesselProgrammingDetail = new VesselProgrammingDetail();
        vesselProgrammingDetail.setId(1);
        mockEir.setVesselProgrammingDetail(vesselProgrammingDetail);
        mockEir.setActive(true);
        mockEir.setEirChassis(mockEirChassis);
        mockEir.setFlagChassisStayed(false);
        mockEir.setTruckArrivalDate(LocalDateTime.now());

        return mockEir;
    }

    Eir generateMockEirGateOutFullWithEirChassis() {
        Eir mockEir = new Eir();
        mockEir.setId(900995);

        Container mockContainer = new Container();
        mockContainer.setId(1);
        mockContainer.setContainerNumber("CNT00000043");

        Truck truck = new Truck();
        Person driverPerson = new Person();

        Catalog catMovement = new Catalog();
        catMovement.setId(888);

        Catalog catEmptyFull = new Catalog();
        catEmptyFull.setId(123);

        Catalog catProcedence = new Catalog();
        catProcedence.setId(1);

        Catalog catCreationorigin = new Catalog();
        catCreationorigin.setId(1);

        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setId(1);

        ChassisDocumentDetail chassisDocumentDetail = new ChassisDocumentDetail();

        EirChassis mockEirChassis = new EirChassis();
        mockEirChassis.setId(1111);
        mockEirChassis.setChassisDocumentDetail(chassisDocumentDetail);
        mockEir.setCatMovement(catMovement);
        mockEir.setCatEmptyFull(catEmptyFull);
        mockEir.setContainer(mockContainer);
        mockEir.setTruck(truck);
        mockEir.setDriverPerson(driverPerson);
        mockEir.setCatOrigin(catProcedence);
        mockEir.setBusinessUnit(businessUnit);
        mockEir.setCatCreationOrigin(catCreationorigin);
        VesselProgrammingDetail vesselProgrammingDetail = new VesselProgrammingDetail();
        vesselProgrammingDetail.setId(1);
        mockEir.setVesselProgrammingDetail(vesselProgrammingDetail);
        mockEir.setActive(true);
        mockEir.setEirChassis(mockEirChassis);
        mockEir.setFlagChassisStayed(false);
        mockEir.setTruckArrivalDate(LocalDateTime.now());

        return mockEir;
    }


    EirChassis generateMockEirChassis() {
        EirChassis mockEirChassis = new EirChassis();
        mockEirChassis.setId(2222);

        Chassis mockChassis = new Chassis();
        mockChassis.setId(11);
        mockChassis.setChassisNumber("CH000002");


        ChassisDocumentDetail chassisDocumentDetail = new ChassisDocumentDetail();
        chassisDocumentDetail.setId(1);
        mockEirChassis.setChassisDocumentDetail(chassisDocumentDetail);
        mockEirChassis.setChassis(mockChassis);
        return mockEirChassis;
    }

    @Test
    void truckDepartureRegister_NullInput() {
        SdgTruckDepartureRegisterOutput response;

        //null ppo
        response = truckDepartureRegisterService.register(null);
        assertNull(response.getRespResult());
        assertNull(response.getResultMessage());
    }


    @Test
    void truckDepartureRegister_GateInEmptyNoMultipleEir_NoChassisEir() {
        SdgTruckDepartureRegisterOutput response;
        List<EirMultiple> mockEirMultipleList = new ArrayList<>();

        Container mockContainer = new Container();
        mockContainer.setId(1);

        when(catalogRepository.findIdByAlias(anyString())).thenReturn(2);

        when(containerRepository.findByContainerNumber(anyString())).thenReturn(mockContainer);

        User user = new User();
        user.setId(1);

        Person userPerson = new Person();
        userPerson.setId(1);

        user.setPerson(userPerson);

        Optional<User> mockUser = Optional.of(user);
        when(userRepository.findById(anyInt())).thenReturn(mockUser);

        when(businessUnitRepository.findParentBusinessUnitId(anyInt())).thenReturn(1);
        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setBusinesUnitAlias("test");
        when(businessUnitRepository.findOneById(anyInt())).thenReturn(Optional.of(businessUnit));

        when(eirMultipleRepository.findByEirIdAndActiveIsTrue(anyInt())).thenReturn(mockEirMultipleList);

        when(eirRepository.findOneById(anyInt())).thenReturn(generateMockEirNoEirChassis());

        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("r_action", "true");

        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);

        Catalog mockCatalog = new Catalog();
        when(catalogRepository.findById(anyInt())).thenReturn(Optional.of(mockCatalog));
        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);

        when(eirRepository.findEirGateInByEirGateOutId(anyInt())).thenReturn(generateMockEirNoEirChassis());
        response = truckDepartureRegisterService.register(root);
        assertEquals(1, response.getRespResult());


    }

    @Test
    void truckDepartureRegister_GateOutEmptyNoMultipleEir_NoChassisEir() {
        SdgTruckDepartureRegisterOutput response;
        List<EirMultiple> mockEirMultipleList = new ArrayList<>();

        Container mockContainer = new Container();
        mockContainer.setId(1);

        when(catalogRepository.findIdByAlias(anyString())).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS)).thenReturn(1);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS)).thenReturn(1);
        when(catalogRepository.findByAlias("sd1_statusinspectiongral_pending")).thenReturn(new Catalog());
        when(containerRepository.findByContainerNumber(anyString())).thenReturn(mockContainer);

        Optional<User> mockUser = Optional.of(new User());
        when(userRepository.findById(anyInt())).thenReturn(mockUser);

        when(businessUnitRepository.findParentBusinessUnitId(anyInt())).thenReturn(1);
        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setBusinesUnitAlias("test");
        when(businessUnitRepository.findOneById(anyInt())).thenReturn(Optional.of(businessUnit));

        when(eirMultipleRepository.findByEirIdAndActiveIsTrue(anyInt())).thenReturn(mockEirMultipleList);

        when(eirRepository.findOneById(anyInt())).thenReturn(generateMockEirNoEirChassis());

        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("r_action", "true");

        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);

        Catalog mockCatalog = new Catalog();
        when(catalogRepository.findById(anyInt())).thenReturn(Optional.of(mockCatalog));
        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);

        when(eirRepository.findEirGateInByEirGateOutId(anyInt())).thenReturn(generateMockEirNoEirChassis());
        response = truckDepartureRegisterService.register(root);
        assertEquals(2, response.getRespResult());


    }


    @Test
    void truckDepartureRegister_GateInEmptyNoMultipleEir_WithChassisEir() {
        SdgTruckDepartureRegisterOutput response;
        List<EirMultiple> mockEirMultipleList = new ArrayList<>();

        Container mockContainer = new Container();
        mockContainer.setId(1);

        when(catalogRepository.findIdByAlias(anyString())).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS)).thenReturn(999);
        when(containerRepository.findByContainerNumber(anyString())).thenReturn(mockContainer);

        Optional<User> mockUser = Optional.of(new User());
        when(userRepository.findById(anyInt())).thenReturn(mockUser);

        when(businessUnitRepository.findParentBusinessUnitId(anyInt())).thenReturn(1);
        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setBusinesUnitAlias("test");
        when(businessUnitRepository.findOneById(anyInt())).thenReturn(Optional.of(businessUnit));

        when(eirMultipleRepository.findByEirIdAndActiveIsTrue(anyInt())).thenReturn(mockEirMultipleList);

        when(eirRepository.findOneById(anyInt())).thenReturn(generateMockEirWithEirChassis());

        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("r_action", "false");

        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);

        Catalog mockCatalog = new Catalog();
        when(catalogRepository.findById(anyInt())).thenReturn(Optional.of(mockCatalog));
        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);

        when(eirRepository.findEirGateInByEirGateOutId(anyInt())).thenReturn(generateMockEirWithEirChassis());

        EirChassis mockEirChassis = new EirChassis();
        mockEirChassis.setId(2222);
        ChassisDocumentDetail chassisDocumentDetail = new ChassisDocumentDetail();
        chassisDocumentDetail.setId(1);
        mockEirChassis.setChassisDocumentDetail(chassisDocumentDetail);

        Optional<EirChassis> optionalEirChassis = Optional.of(generateMockEirChassis());
        when(eirChassisRepository.findById(any())).thenReturn(optionalEirChassis);

        Chassis mockChassis = new Chassis();
        mockChassis.setId(333);
        mockChassis.setChassisNumber("CH00043");
        Optional<Chassis> optionalChassis = Optional.of(mockChassis);
        when(chassisRepository.findById(anyInt())).thenReturn(optionalChassis);

        when(eirRepository.findEirByChassisId(any(), any(), any(),
                any(), any(), any(), any(), any())).thenReturn(generateMockEirWithEirChassis());

        doReturn(1).when(chassisDocumentDetailRepository).findDocumentChassisBookingIdByDocumentChassisDetailId(anyInt());
        ChassisDocumentDetail mockChassisDocumentDetail = new ChassisDocumentDetail();
        mockChassisDocumentDetail.setId(4);
        Optional<ChassisDocumentDetail> optionalChassisDocumentDetail = Optional.of(mockChassisDocumentDetail);
        doReturn(optionalChassisDocumentDetail).when(chassisDocumentDetailRepository).findById(any());

        doReturn(1).when(stockChassisRepository).findEirChassisGateOutIdByEirChassisId(anyInt());

        ChassisBookingDocument chassisBookingDocument = new ChassisBookingDocument();
        chassisBookingDocument.setId(877);
        doReturn(Optional.of(chassisBookingDocument)).when(chassisBookingDocumentRepository).findById(anyInt());


        doReturn(generateMockEirWithEirChassis()).when(eirRepository).findByEirIdAndActive(anyInt());
        doReturn(generateMockEirChassis()).when(eirChassisRepository).findEirChassisByIdAndActive(anyInt());
        when(messageLanguageService.getMessage(anyString(), anyInt(), anyInt())).thenReturn(anyString());
        response = truckDepartureRegisterService.register(root);
        assertEquals(2, response.getRespResult());
    }

    @Test
    void truckDepartureRegister_GateInFullNoMultipleEir_WithChassisEir() {
        String jsonArray = "[{\"nombre\":\"Archivo1\",\"peso\":\"109\",\"formato\":\"PDF\",\"ubicacion\":\"/documents/reports\",\"url\":\"https://example.com/documents/reports/archivo1.pdf\",\"id\":\"12345\",\"tipoAdjunto\":1},{\"nombre\":\"Imagen2\",\"peso\":\"3\",\"formato\":\"PNG\",\"ubicacion\":\"/images/gallery\",\"url\":\"https://example.com/images/gallery/imagen2.png\",\"id\":\"67890\",\"tipoAdjunto\":2},{\"nombre\":\"Video3\",\"peso\":\"98\",\"formato\":\"MP4\",\"ubicacion\":\"/videos/tutorials\",\"url\":\"https://example.com/videos/tutorials/video3.mp4\",\"id\":\"54321\",\"tipoAdjunto\":3}]";
        root.getSdg().getInput().setPhotos(jsonArray);
        root.getSdg().getInput().setReject(false);
        SdgTruckDepartureRegisterOutput response;
        List<EirMultiple> mockEirMultipleList = new ArrayList<>();

        Container mockContainer = new Container();
        mockContainer.setId(1);

        when(catalogRepository.findIdByAlias(anyString())).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS)).thenReturn(999);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS)).thenReturn(2);
        when(containerRepository.findByContainerNumber(anyString())).thenReturn(mockContainer);

        Optional<User> mockUser = Optional.of(new User());
        when(userRepository.findById(anyInt())).thenReturn(mockUser);

        when(businessUnitRepository.findParentBusinessUnitId(anyInt())).thenReturn(1);
        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setBusinesUnitAlias("test");
        when(businessUnitRepository.findOneById(anyInt())).thenReturn(Optional.of(businessUnit));

        when(eirMultipleRepository.findByEirIdAndActiveIsTrue(anyInt())).thenReturn(mockEirMultipleList);

        when(eirRepository.findOneById(anyInt())).thenReturn(generateMockEirFullWithEirChassis());

        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("r_action", "false");

        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);

        Catalog mockCatalog = new Catalog();
        when(catalogRepository.findById(anyInt())).thenReturn(Optional.of(mockCatalog));
        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);


        when(eirRepository.findEirGateInByEirGateOutId(anyInt())).thenReturn(generateMockEirFullWithEirChassis());

        EirChassis mockEirChassis = new EirChassis();
        mockEirChassis.setId(2222);
        ChassisDocumentDetail chassisDocumentDetail = new ChassisDocumentDetail();
        chassisDocumentDetail.setId(1);
        mockEirChassis.setChassisDocumentDetail(chassisDocumentDetail);

        Optional<EirChassis> optionalEirChassis = Optional.of(generateMockEirChassis());
        when(eirChassisRepository.findById(any())).thenReturn(optionalEirChassis);

        Chassis mockChassis = new Chassis();
        mockChassis.setId(333);
        mockChassis.setChassisNumber("CH00043");
        Optional<Chassis> optionalChassis = Optional.of(mockChassis);
        when(chassisRepository.findById(anyInt())).thenReturn(optionalChassis);

        when(eirRepository.findEirByChassisId(any(), any(), any(),
                any(), any(), any(), any(), any())).thenReturn(generateMockEirWithEirChassis());

        doReturn(1).when(chassisDocumentDetailRepository).findDocumentChassisBookingIdByDocumentChassisDetailId(anyInt());
        ChassisDocumentDetail mockChassisDocumentDetail = new ChassisDocumentDetail();
        mockChassisDocumentDetail.setId(4);
        Optional<ChassisDocumentDetail> optionalChassisDocumentDetail = Optional.of(mockChassisDocumentDetail);
        doReturn(optionalChassisDocumentDetail).when(chassisDocumentDetailRepository).findById(any());

        doReturn(1).when(stockChassisRepository).findEirChassisGateOutIdByEirChassisId(anyInt());

        ChassisBookingDocument chassisBookingDocument = new ChassisBookingDocument();
        chassisBookingDocument.setId(877);
        doReturn(Optional.of(chassisBookingDocument)).when(chassisBookingDocumentRepository).findById(anyInt());

        doReturn(generateMockEirWithEirChassis()).when(eirRepository).findByEirIdAndActive(anyInt());
        doReturn(generateMockEirChassis()).when(eirChassisRepository).findEirChassisByIdAndActive(anyInt());

        CargoDocumentDetail cargoDocumentDetail = new CargoDocumentDetail();
        cargoDocumentDetail.setId(11);
        doReturn(cargoDocumentDetail).when(cargoDocumentDetailRepository).findByEirId(anyInt());

        VesselProgrammingContainer vesselProgrammingContainer = new VesselProgrammingContainer();
        doReturn(Optional.of(vesselProgrammingContainer)).when(vesselProgrammingContainerRepository).findByVesselProgrammingDetailIdAndContainerIdAndActive(anyInt(), anyInt(), anyBoolean());

        when(messageLanguageService.getMessage(anyString(), anyInt(), anyInt())).thenReturn(anyString());
        response = truckDepartureRegisterService.register(root);
        assertEquals(2, response.getRespResult());
    }

    @Test
    void truckDepartureRegister_GateOutEmptyNoMultipleEir_WithChassisEir() {
        SdgTruckDepartureRegisterOutput response;
        List<EirMultiple> mockEirMultipleList = new ArrayList<>();

        Container mockContainer = new Container();
        mockContainer.setId(1);

        when(catalogRepository.findIdByAlias(anyString())).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS)).thenReturn(888);
        when(containerRepository.findByContainerNumber(anyString())).thenReturn(mockContainer);

        Optional<User> mockUser = Optional.of(new User());
        when(userRepository.findById(anyInt())).thenReturn(mockUser);

        when(businessUnitRepository.findParentBusinessUnitId(anyInt())).thenReturn(1);
        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setBusinesUnitAlias("test");
        when(businessUnitRepository.findOneById(anyInt())).thenReturn(Optional.of(businessUnit));

        when(eirMultipleRepository.findByEirIdAndActiveIsTrue(anyInt())).thenReturn(mockEirMultipleList);

        when(eirRepository.findOneById(anyInt())).thenReturn(generateMockEirGateOutWithEirChassis());

        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("r_action", "true");

        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);

        Catalog mockCatalog = new Catalog();
        when(catalogRepository.findById(anyInt())).thenReturn(Optional.of(mockCatalog));
        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);


        when(eirRepository.findEirGateInByEirGateOutId(anyInt())).thenReturn(generateMockEirGateOutWithEirChassis());

        EirChassis mockEirChassis = new EirChassis();
        mockEirChassis.setId(2222);
        ChassisDocumentDetail chassisDocumentDetail = new ChassisDocumentDetail();
        chassisDocumentDetail.setId(1);
        mockEirChassis.setChassisDocumentDetail(chassisDocumentDetail);

        Optional<EirChassis> optionalEirChassis = Optional.of(generateMockEirChassis());
        when(eirChassisRepository.findById(anyInt())).thenReturn(optionalEirChassis);

        Chassis mockChassis = new Chassis();
        mockChassis.setId(333);
        mockChassis.setChassisNumber("CH00043");
        Optional<Chassis> optionalChassis = Optional.of(mockChassis);
        when(chassisRepository.findById(anyInt())).thenReturn(optionalChassis);

        when(eirRepository.findEirByChassisId(any(), any(), any(),
                any(), any(), any(), any(), any())).thenReturn(generateMockEirGateOutWithEirChassis());

        doReturn(1).when(chassisDocumentDetailRepository).findDocumentChassisBookingIdByDocumentChassisDetailId(anyInt());

        ChassisDocumentDetail mockChassisDocumentDetail = new ChassisDocumentDetail();
        mockChassisDocumentDetail.setId(4);
        Optional<ChassisDocumentDetail> optionalChassisDocumentDetail = Optional.of(mockChassisDocumentDetail);
        doReturn(optionalChassisDocumentDetail).when(chassisDocumentDetailRepository).findById(any());

        doReturn(1).when(stockChassisRepository).findEirChassisGateOutIdByEirChassisId(anyInt());


        ChassisBookingDocument chassisBookingDocument = new ChassisBookingDocument();
        chassisBookingDocument.setId(877);
        doReturn(Optional.of(chassisBookingDocument)).when(chassisBookingDocumentRepository).findById(anyInt());


        doReturn(generateMockEirWithEirChassis()).when(eirRepository).findByEirIdAndActive(anyInt());
        doReturn(generateMockEirChassis()).when(eirChassisRepository).findEirChassisByIdAndActive(anyInt());
        when(messageLanguageService.getMessage(anyString(), anyInt(), anyInt())).thenReturn(anyString());
        response = truckDepartureRegisterService.register(root);
        assertEquals(2, response.getRespResult());

    }

    @Test
    void truckDepartureRegister_GateOutFullNoMultipleEir_WithChassisEir() {
        SdgTruckDepartureRegisterOutput response;
        List<EirMultiple> mockEirMultipleList = new ArrayList<>();

        Container mockContainer = new Container();
        mockContainer.setId(1);
        when(chassisRepository.findChassisId(any(), any())).thenReturn(null);
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS)).thenReturn(888);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS)).thenReturn(123);
        when(containerRepository.findByContainerNumber(anyString())).thenReturn(mockContainer);

        User user = new User();
        user.setId(1);

        Person userPerson = new Person();
        userPerson.setId(1);

        user.setPerson(userPerson);

        Optional<User> mockUser = Optional.of(user);
        when(userRepository.findById(anyInt())).thenReturn(mockUser);

        when(businessUnitRepository.findParentBusinessUnitId(anyInt())).thenReturn(1);
        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setBusinesUnitAlias("test");
        when(businessUnitRepository.findOneById(anyInt())).thenReturn(Optional.of(businessUnit));

        when(eirMultipleRepository.findByEirIdAndActiveIsTrue(anyInt())).thenReturn(mockEirMultipleList);

        when(eirRepository.findOneById(anyInt())).thenReturn(generateMockEirGateOutFullWithEirChassis());

        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("r_action", "true");

        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);

        Catalog mockCatalog = new Catalog();
        when(catalogRepository.findById(any())).thenReturn(Optional.of(mockCatalog));

        when(catalogRepository.findByAlias(anyString())).thenReturn(mockCatalog);
        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);


        when(eirRepository.findEirGateInByEirGateOutId(anyInt())).thenReturn(generateMockEirGateOutFullWithEirChassis());

        EirChassis mockEirChassis = new EirChassis();
        mockEirChassis.setId(2222);
        ChassisDocumentDetail chassisDocumentDetail = new ChassisDocumentDetail();
        chassisDocumentDetail.setId(1);
        mockEirChassis.setChassisDocumentDetail(chassisDocumentDetail);

        Optional<EirChassis> optionalEirChassis = Optional.of(generateMockEirChassis());
        when(eirChassisRepository.findById(anyInt())).thenReturn(optionalEirChassis);


        Optional<Chassis> optionalChassis = Optional.empty();
        when(chassisRepository.findById(anyInt())).thenReturn(optionalChassis);

        when(eirRepository.findEirByChassisId(any(), any(), any(),
                any(), any(), any(), any(), any())).thenReturn(null);

        doReturn(1).when(chassisDocumentDetailRepository).findDocumentChassisBookingIdByDocumentChassisDetailId(anyInt());

        ChassisDocumentDetail mockChassisDocumentDetail = new ChassisDocumentDetail();
        mockChassisDocumentDetail.setId(4);
        Optional<ChassisDocumentDetail> optionalChassisDocumentDetail = Optional.of(mockChassisDocumentDetail);
        doReturn(optionalChassisDocumentDetail).when(chassisDocumentDetailRepository).findById(any());

        doReturn(1).when(stockChassisRepository).findEirChassisGateOutIdByEirChassisId(anyInt());


        ChassisBookingDocument chassisBookingDocument = new ChassisBookingDocument();
        chassisBookingDocument.setId(877);
        doReturn(Optional.of(chassisBookingDocument)).when(chassisBookingDocumentRepository).findById(anyInt());


        doReturn(generateMockEirWithEirChassis()).when(eirRepository).findByEirIdAndActive(anyInt());
        doReturn(generateMockEirChassis()).when(eirChassisRepository).findEirChassisByIdAndActive(anyInt());

        when(sdgStockFullRepository.getEirAuxByEirId(anyInt())).thenReturn(null);


        doReturn(null).when(sdgStockEmptyRepository).findGateInEirByContainerIdAndEirId(anyInt(), anyInt());

        ContainerRestriction containerRestriction = new ContainerRestriction();
        containerRestriction.setId(1);
        doReturn(null).when(containerRestrictionRepository).findContainerRestrictionByParameters(anyInt(), anyInt(), anyInt());

        CargoDocumentDetail cargoDocumentDetail = new CargoDocumentDetail();
        cargoDocumentDetail.setId(11);
        doReturn(cargoDocumentDetail).when(cargoDocumentDetailRepository).findByEirId(anyInt());

        when(eirRepository.findEirIdByEirChassisId(anyInt())).thenReturn(null);
        when(messageLanguageService.getMessage(anyString(), anyInt(), anyInt())).thenReturn("Default message");

        VesselProgrammingContainer vesselProgrammingContainer = new VesselProgrammingContainer();
        doReturn(Optional.of(vesselProgrammingContainer)).when(vesselProgrammingContainerRepository).findByVesselProgrammingDetailIdAndContainerIdAndActive(anyInt(), anyInt(), anyBoolean());
        when(messageLanguageService.getMessage(anyString(), anyInt(), anyInt(), any())).thenReturn("Default message");
        response = truckDepartureRegisterService.register(root);
        assertEquals(1, response.getRespResult());

    }

    @Test
    void truckDepartureRegister_GateInNoMultipleEir_WithChassisEir_test() {
        SdgTruckDepartureRegisterOutput response;
        List<EirMultiple> mockEirMultipleList = new ArrayList<>();

        Container mockContainer = new Container();
        mockContainer.setId(1);
        when(chassisRepository.findChassisId(any(), any())).thenReturn(null);
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS)).thenReturn(888);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS)).thenReturn(123);
        when(containerRepository.findByContainerNumber(anyString())).thenReturn(mockContainer);

        User user = new User();
        user.setId(1);

        Person userPerson = new Person();
        userPerson.setId(1);

        user.setPerson(userPerson);

        Optional<User> mockUser = Optional.of(user);
        when(userRepository.findById(anyInt())).thenReturn(mockUser);

        when(businessUnitRepository.findParentBusinessUnitId(anyInt())).thenReturn(1);
        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setBusinesUnitAlias("test");
        when(businessUnitRepository.findOneById(anyInt())).thenReturn(Optional.of(businessUnit));

        when(eirMultipleRepository.findByEirIdAndActiveIsTrue(anyInt())).thenReturn(mockEirMultipleList);

        when(eirRepository.findOneById(anyInt())).thenReturn(generateMockEirGateOutFullWithEirChassis());

        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("r_action", "true");

        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);

        Catalog mockCatalog = new Catalog();
        when(catalogRepository.findById(any())).thenReturn(Optional.of(mockCatalog));

        when(catalogRepository.findByAlias(anyString())).thenReturn(mockCatalog);
        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);

        Eir eir = generateMockEirGateOutFullWithEirChassis();
        when(eirRepository.findEirGateInByEirGateOutId(anyInt())).thenReturn(null);

        EirChassis mockEirChassis = new EirChassis();
        mockEirChassis.setId(2222);
        ChassisDocumentDetail chassisDocumentDetail = new ChassisDocumentDetail();
        chassisDocumentDetail.setId(1);
        mockEirChassis.setChassisDocumentDetail(chassisDocumentDetail);

        Optional<EirChassis> optionalEirChassis = Optional.of(generateMockEirChassis());
        when(eirChassisRepository.findById(anyInt())).thenReturn(optionalEirChassis);


        Optional<Chassis> optionalChassis = Optional.empty();
        when(chassisRepository.findById(anyInt())).thenReturn(optionalChassis);

        when(eirRepository.findEirByChassisId(any(), any(), any(),
                any(), any(), any(), any(), any())).thenReturn(null);

        doReturn(1).when(chassisDocumentDetailRepository).findDocumentChassisBookingIdByDocumentChassisDetailId(anyInt());

        ChassisDocumentDetail mockChassisDocumentDetail = new ChassisDocumentDetail();
        mockChassisDocumentDetail.setId(4);
        Optional<ChassisDocumentDetail> optionalChassisDocumentDetail = Optional.of(mockChassisDocumentDetail);
        doReturn(optionalChassisDocumentDetail).when(chassisDocumentDetailRepository).findById(any());

        doReturn(1).when(stockChassisRepository).findEirChassisGateOutIdByEirChassisId(anyInt());


        ChassisBookingDocument chassisBookingDocument = new ChassisBookingDocument();
        chassisBookingDocument.setId(877);
        doReturn(Optional.of(chassisBookingDocument)).when(chassisBookingDocumentRepository).findById(anyInt());


        doReturn(generateMockEirWithEirChassis()).when(eirRepository).findByEirIdAndActive(anyInt());
        doReturn(generateMockEirChassis()).when(eirChassisRepository).findEirChassisByIdAndActive(anyInt());

        when(sdgStockFullRepository.getEirAuxByEirId(anyInt())).thenReturn(null);


        doReturn(null).when(sdgStockEmptyRepository).findGateInEirByContainerIdAndEirId(anyInt(), anyInt());

        ContainerRestriction containerRestriction = new ContainerRestriction();
        containerRestriction.setId(1);

        doReturn(null).when(containerRestrictionRepository).findContainerRestrictionByParameters(anyInt(), anyInt(), anyInt());

        CargoDocumentDetail cargoDocumentDetail = new CargoDocumentDetail();
        cargoDocumentDetail.setId(11);
        doReturn(cargoDocumentDetail).when(cargoDocumentDetailRepository).findByEirId(anyInt());

        when(eirRepository.findEirIdByEirChassisId(anyInt())).thenReturn(null);
        when(messageLanguageService.getMessage(anyString(), anyInt(), anyInt())).thenReturn("Default message");

        VesselProgrammingContainer vesselProgrammingContainer = new VesselProgrammingContainer();
        doReturn(Optional.of(vesselProgrammingContainer)).when(vesselProgrammingContainerRepository).findByVesselProgrammingDetailIdAndContainerIdAndActive(anyInt(), anyInt(), anyBoolean());
        when(messageLanguageService.getMessage(anyString(), anyInt(), anyInt(), any())).thenReturn("Default message");

        EirDocumentCargoDetail eirDocumentCargoDetail = new EirDocumentCargoDetail();
        CargoDocumentDetail cargoDocumentDetail1 = new CargoDocumentDetail();
        eirDocumentCargoDetail.setCargoDocumentDetail(cargoDocumentDetail1);
        eirDocumentCargoDetail.setEir(eir);
        when(eirDocumentCargoDetailRepository.findManyByEirId(any())).thenReturn(List.of(eirDocumentCargoDetail));


        when(vesselProgrammingContainerRepository.findByEirIdList(any())).thenReturn(List.of(vesselProgrammingContainer));

        root.getSdg().getInput().setReject(false);
        response = truckDepartureRegisterService.register(root);
        assertEquals(1, response.getRespResult());

    }

    @Test
    void truckDepartureRegister_GateInEmptyMultipleEir() {
        SdgTruckDepartureRegisterOutput response;
        List<EirMultiple> mockEirMultipleList = new ArrayList<>();
        EirMultiple eirMultiple = new EirMultiple();
        eirMultiple.setEir(generateMockEirWithEirChassis());
        eirMultiple.setActive(true);
        EirMultipleId id = new EirMultipleId();
        id.setEirId(111);
        id.setEirMultipleId(222);
        eirMultiple.setId(id);
        mockEirMultipleList.add(eirMultiple);

        Container mockContainer = new Container();
        mockContainer.setId(1);
        when(chassisRepository.findChassisId(any(), any())).thenReturn(null);
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS)).thenReturn(888);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS)).thenReturn(123);
        when(containerRepository.findByContainerNumber(anyString())).thenReturn(mockContainer);

        Optional<User> mockUser = Optional.of(new User());
        when(userRepository.findById(anyInt())).thenReturn(mockUser);

        when(businessUnitRepository.findParentBusinessUnitId(anyInt())).thenReturn(1);
        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setBusinesUnitAlias("test");
        when(businessUnitRepository.findOneById(anyInt())).thenReturn(Optional.of(businessUnit));

        when(eirMultipleRepository.findByEirIdAndActiveIsTrue(anyInt())).thenReturn(mockEirMultipleList);

        when(eirRepository.findOneById(anyInt())).thenReturn(generateMockEirNoEirChassis());

        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("r_action", "true");

        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);

        Catalog mockCatalog = new Catalog();
        when(catalogRepository.findById(any())).thenReturn(Optional.of(mockCatalog));

        when(catalogRepository.findByAlias(anyString())).thenReturn(mockCatalog);
        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);


        when(eirRepository.findEirGateInByEirGateOutId(anyInt())).thenReturn(generateMockEirWithEirChassis());

        EirChassis mockEirChassis = new EirChassis();
        mockEirChassis.setId(2222);
        ChassisDocumentDetail chassisDocumentDetail = new ChassisDocumentDetail();
        chassisDocumentDetail.setId(1);
        mockEirChassis.setChassisDocumentDetail(chassisDocumentDetail);

        Optional<EirChassis> optionalEirChassis = Optional.of(generateMockEirChassis());
        when(eirChassisRepository.findById(anyInt())).thenReturn(optionalEirChassis);


        Optional<Chassis> optionalChassis = Optional.empty();
        when(chassisRepository.findById(anyInt())).thenReturn(optionalChassis);

        when(eirRepository.findEirByChassisId(any(), any(), any(),
                any(), any(), any(), any(), any())).thenReturn(null);

        doReturn(1).when(chassisDocumentDetailRepository).findDocumentChassisBookingIdByDocumentChassisDetailId(anyInt());

        ChassisDocumentDetail mockChassisDocumentDetail = new ChassisDocumentDetail();
        mockChassisDocumentDetail.setId(4);
        Optional<ChassisDocumentDetail> optionalChassisDocumentDetail = Optional.of(mockChassisDocumentDetail);
        doReturn(optionalChassisDocumentDetail).when(chassisDocumentDetailRepository).findById(any());

        doReturn(1).when(stockChassisRepository).findEirChassisGateOutIdByEirChassisId(anyInt());


        ChassisBookingDocument chassisBookingDocument = new ChassisBookingDocument();
        chassisBookingDocument.setId(877);
        doReturn(Optional.of(chassisBookingDocument)).when(chassisBookingDocumentRepository).findById(anyInt());

        List<Eir> eirList = new ArrayList<>();
        eirList.add(generateMockEirWithEirChassis());
        eirList.add(generateMockEirNoEirChassis());
        when(eirRepository.findByEirMultipleId(any())).thenReturn(eirList);

        doReturn(generateMockEirWithEirChassis()).when(eirRepository).findByEirIdAndActive(anyInt());
        doReturn(generateMockEirChassis()).when(eirChassisRepository).findEirChassisByIdAndActive(anyInt());

        when(sdgStockFullRepository.getEirAuxByEirId(anyInt())).thenReturn(null);


        doReturn(null).when(sdgStockEmptyRepository).findGateInEirByContainerIdAndEirId(anyInt(), anyInt());

        ContainerRestriction containerRestriction = new ContainerRestriction();
        containerRestriction.setId(1);
        doReturn(null).when(containerRestrictionRepository).findContainerRestrictionByParameters(anyInt(), anyInt(), anyInt());

        CargoDocumentDetail cargoDocumentDetail = new CargoDocumentDetail();
        cargoDocumentDetail.setId(11);
        doReturn(cargoDocumentDetail).when(cargoDocumentDetailRepository).findByEirId(anyInt());

        when(eirRepository.findEirIdByEirChassisId(anyInt())).thenReturn(null);
        when(messageLanguageService.getMessage(anyString(), anyInt(), anyInt())).thenReturn("Default message");

        VesselProgrammingContainer vesselProgrammingContainer = new VesselProgrammingContainer();
        doReturn(Optional.of(vesselProgrammingContainer)).when(vesselProgrammingContainerRepository).findByVesselProgrammingDetailIdAndContainerIdAndActive(anyInt(), anyInt(), anyBoolean());
        when(messageLanguageService.getMessage(anyString(), anyInt(), anyInt(), any())).thenReturn("Default message");
        response = truckDepartureRegisterService.register(root);
        assertEquals(1, response.getRespResult());
    }


    @Test
    void truckDepartureRegister_GateInEmptyMultipleEir2() {
        SdgTruckDepartureRegisterOutput response;
        List<EirMultiple> mockEirMultipleList = new ArrayList<>();
        EirMultiple eirMultiple = new EirMultiple();
        eirMultiple.setEir(generateMockEirWithEirChassis());
        eirMultiple.setActive(true);
        EirMultipleId id = new EirMultipleId();
        id.setEirId(111);
        id.setEirMultipleId(222);
        eirMultiple.setId(id);
        mockEirMultipleList.add(eirMultiple);

        Container mockContainer = new Container();
        mockContainer.setId(1);
        when(chassisRepository.findChassisId(any(), any())).thenReturn(null);
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS)).thenReturn(999);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS)).thenReturn(123);
        when(containerRepository.findByContainerNumber(anyString())).thenReturn(mockContainer);

        Optional<User> mockUser = Optional.of(new User());
        when(userRepository.findById(anyInt())).thenReturn(mockUser);

        when(businessUnitRepository.findParentBusinessUnitId(anyInt())).thenReturn(1);
        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setBusinesUnitAlias("test");
        when(businessUnitRepository.findOneById(anyInt())).thenReturn(Optional.of(businessUnit));

        when(eirMultipleRepository.findByEirIdAndActiveIsTrue(anyInt())).thenReturn(mockEirMultipleList);

        when(eirRepository.findOneById(anyInt())).thenReturn(generateMockEirNoEirChassis());

        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("r_action", "true");

        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);

        Catalog mockCatalog = new Catalog();
        when(catalogRepository.findById(any())).thenReturn(Optional.of(mockCatalog));

        when(catalogRepository.findByAlias(anyString())).thenReturn(mockCatalog);
        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);


        when(eirRepository.findEirGateInByEirGateOutId(anyInt())).thenReturn(generateMockEirWithEirChassis());

        EirChassis mockEirChassis = new EirChassis();
        mockEirChassis.setId(2222);
        ChassisDocumentDetail chassisDocumentDetail = new ChassisDocumentDetail();
        chassisDocumentDetail.setId(1);
        mockEirChassis.setChassisDocumentDetail(chassisDocumentDetail);

        Optional<EirChassis> optionalEirChassis = Optional.of(generateMockEirChassis());
        when(eirChassisRepository.findById(anyInt())).thenReturn(optionalEirChassis);


        Optional<Chassis> optionalChassis = Optional.empty();
        when(chassisRepository.findById(anyInt())).thenReturn(optionalChassis);

        when(eirRepository.findEirByChassisId(any(), any(), any(),
                any(), any(), any(), any(), any())).thenReturn(generateMockEirFullWithEirChassis());

        doReturn(1).when(chassisDocumentDetailRepository).findDocumentChassisBookingIdByDocumentChassisDetailId(anyInt());

        ChassisDocumentDetail mockChassisDocumentDetail = new ChassisDocumentDetail();
        mockChassisDocumentDetail.setId(4);
        ChassisBookingDocument chassisBookingDocument = new ChassisBookingDocument();
        chassisBookingDocument.setId(1);
        mockChassisDocumentDetail.setChassisBookingDocument(chassisBookingDocument);
        Optional<ChassisDocumentDetail> optionalChassisDocumentDetail = Optional.of(mockChassisDocumentDetail);
        doReturn(optionalChassisDocumentDetail).when(chassisDocumentDetailRepository).findById(any());

        doReturn(1).when(stockChassisRepository).findEirChassisGateOutIdByEirChassisId(anyInt());


        ChassisBookingDocument chassisBookingDocument2 = new ChassisBookingDocument();
        chassisBookingDocument2.setId(877);
        doReturn(Optional.of(chassisBookingDocument2)).when(chassisBookingDocumentRepository).findById(anyInt());

        List<Eir> eirList = new ArrayList<>();
        eirList.add(generateMockEirWithEirChassis());
        eirList.add(generateMockEirNoEirChassis());
        when(eirRepository.findByEirMultipleId(any())).thenReturn(eirList);

        doReturn(generateMockEirWithEirChassis()).when(eirRepository).findByEirIdAndActive(anyInt());
        doReturn(generateMockEirChassis()).when(eirChassisRepository).findEirChassisByIdAndActive(anyInt());

        when(sdgStockFullRepository.getEirAuxByEirId(anyInt())).thenReturn(null);


        doReturn(null).when(sdgStockEmptyRepository).findGateInEirByContainerIdAndEirId(anyInt(), anyInt());

        doReturn(null).when(containerRestrictionRepository).findContainerRestrictionByParameters(anyInt(), anyInt(), anyInt());

        CargoDocumentDetail cargoDocumentDetail = new CargoDocumentDetail();
        cargoDocumentDetail.setId(11);
        doReturn(cargoDocumentDetail).when(cargoDocumentDetailRepository).findByEirId(anyInt());

        when(eirRepository.findEirIdByEirChassisId(anyInt())).thenReturn(null);
        when(messageLanguageService.getMessage(anyString(), anyInt(), anyInt())).thenReturn("Default message");

        VesselProgrammingContainer vesselProgrammingContainer = new VesselProgrammingContainer();
        doReturn(Optional.of(vesselProgrammingContainer)).when(vesselProgrammingContainerRepository).findByVesselProgrammingDetailIdAndContainerIdAndActive(anyInt(), anyInt(), anyBoolean());
        when(messageLanguageService.getMessage(anyString(), anyInt(), anyInt(), any())).thenReturn("Default message");
        response = truckDepartureRegisterService.register(root);
        assertEquals(1, response.getRespResult());
    }

    @Test
    void truckDepartureRegister_GateOutFullNoMultipleEir_WithChassisEir_WithContainerRestriction() {
        root.getSdg().getInput().setReject(false);
        SdgTruckDepartureRegisterOutput response;
        List<EirMultiple> mockEirMultipleList = new ArrayList<>();

        Container mockContainer = new Container();
        mockContainer.setId(1);
        when(chassisRepository.findChassisId(any(), any())).thenReturn(null);
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS)).thenReturn(888);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS)).thenReturn(123);
        when(containerRepository.findByContainerNumber(anyString())).thenReturn(mockContainer);
        when(containerRepository.findContainerNumberById(anyInt())).thenReturn("ABCD00004");

        User user = new User();
        user.setId(1);

        Person userPerson = new Person();
        userPerson.setId(1);

        user.setPerson(userPerson);

        Optional<User> mockUser = Optional.of(user);
        when(userRepository.findById(anyInt())).thenReturn(mockUser);

        when(businessUnitRepository.findParentBusinessUnitId(anyInt())).thenReturn(1);
        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setBusinesUnitAlias("test");
        when(businessUnitRepository.findOneById(anyInt())).thenReturn(Optional.of(businessUnit));

        when(eirMultipleRepository.findByEirIdAndActiveIsTrue(anyInt())).thenReturn(mockEirMultipleList);

        when(eirRepository.findOneById(anyInt())).thenReturn(generateMockEirGateOutFullWithEirChassis());

        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("r_action", "true");

        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);

        Catalog mockCatalog = new Catalog();
        when(catalogRepository.findById(any())).thenReturn(Optional.of(mockCatalog));

        when(catalogRepository.findByAlias(anyString())).thenReturn(mockCatalog);
        when(systemRuleRepository.ruleGeneralGetOut(anyString(), anyString(), anyString())).thenReturn(hashMap);


        when(eirRepository.findEirGateInByEirGateOutId(anyInt())).thenReturn(generateMockEirGateOutFullWithEirChassis());

        EirChassis mockEirChassis = new EirChassis();
        mockEirChassis.setId(2222);
        ChassisDocumentDetail chassisDocumentDetail = new ChassisDocumentDetail();
        chassisDocumentDetail.setId(1);
        mockEirChassis.setChassisDocumentDetail(chassisDocumentDetail);

        Optional<EirChassis> optionalEirChassis = Optional.of(generateMockEirChassis());
        when(eirChassisRepository.findById(anyInt())).thenReturn(optionalEirChassis);


        Optional<Chassis> optionalChassis = Optional.empty();
        when(chassisRepository.findById(anyInt())).thenReturn(optionalChassis);

        when(eirRepository.findEirByChassisId(any(), any(), any(),
                any(), any(), any(), any(), any())).thenReturn(null);

        doReturn(1).when(chassisDocumentDetailRepository).findDocumentChassisBookingIdByDocumentChassisDetailId(anyInt());

        ChassisDocumentDetail mockChassisDocumentDetail = new ChassisDocumentDetail();
        mockChassisDocumentDetail.setId(4);
        Optional<ChassisDocumentDetail> optionalChassisDocumentDetail = Optional.of(mockChassisDocumentDetail);
        doReturn(optionalChassisDocumentDetail).when(chassisDocumentDetailRepository).findById(any());

        doReturn(1).when(stockChassisRepository).findEirChassisGateOutIdByEirChassisId(anyInt());


        ChassisBookingDocument chassisBookingDocument = new ChassisBookingDocument();
        chassisBookingDocument.setId(877);
        doReturn(Optional.of(chassisBookingDocument)).when(chassisBookingDocumentRepository).findById(anyInt());


        doReturn(generateMockEirWithEirChassis()).when(eirRepository).findByEirIdAndActive(anyInt());
        doReturn(generateMockEirChassis()).when(eirChassisRepository).findEirChassisByIdAndActive(anyInt());

        when(sdgStockFullRepository.getEirAuxByEirId(anyInt())).thenReturn(null);


        doReturn(null).when(sdgStockEmptyRepository).findGateInEirByContainerIdAndEirId(anyInt(), anyInt());

        ContainerRestriction containerRestriction = new ContainerRestriction();
        containerRestriction.setId(1);
        Set<ContainerRestrictionDetail> containerRestrictionDetails = new HashSet<>();
        containerRestriction.setContainerRestrictionDetails(containerRestrictionDetails);
        doReturn(containerRestriction).when(containerRestrictionRepository).findContainerRestrictionByParameters(anyInt(), anyInt(), anyInt());

        CargoDocumentDetail cargoDocumentDetail = new CargoDocumentDetail();
        cargoDocumentDetail.setId(11);
        doReturn(cargoDocumentDetail).when(cargoDocumentDetailRepository).findByEirId(anyInt());

        when(eirRepository.findEirIdByEirChassisId(anyInt())).thenReturn(null);
        when(messageLanguageService.getMessage(anyString(), anyInt(), anyInt())).thenReturn("Default message");

        VesselProgrammingContainer vesselProgrammingContainer = new VesselProgrammingContainer();
        doReturn(Optional.of(vesselProgrammingContainer)).when(vesselProgrammingContainerRepository).findByVesselProgrammingDetailIdAndContainerIdAndActive(anyInt(), anyInt(), anyBoolean());
        when(messageLanguageService.getMessage(anyString(), anyInt(), anyInt(), any())).thenReturn("Default message");
        response = truckDepartureRegisterService.register(root);
        assertEquals(2, response.getRespResult());

    }

    @Test
    void truckDepartureRegister_GateInEmptyNoMultipleEir() {
        root.getSdg().getInput().setReject(false);
        root.getSdg().getInput().setPassRestriction(null);
        SdgTruckDepartureRegisterOutput response;


        Container mockContainer = new Container();
        mockContainer.setId(1);
        when(chassisRepository.findChassisId(any(), any())).thenReturn(null);
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS)).thenReturn(888);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS)).thenReturn(123);
        when(containerRepository.findByContainerNumber(anyString())).thenReturn(mockContainer);
        when(containerRepository.findContainerNumberById(anyInt())).thenReturn("ABCD00004");
        Optional<User> mockUser = Optional.of(new User());
        when(userRepository.findById(anyInt())).thenReturn(mockUser);

        when(businessUnitRepository.findParentBusinessUnitId(anyInt())).thenReturn(1);
        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setBusinesUnitAlias("test");
        when(businessUnitRepository.findOneById(anyInt())).thenReturn(Optional.of(businessUnit));


        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("r_action", "true");

        when(systemRuleRepository.ruleGeneralGetOut(any(), any(), any())).thenReturn(hashMap);
        Catalog mockCatalog = new Catalog();
        when(catalogRepository.findById(any())).thenReturn(Optional.of(mockCatalog));
        when(catalogRepository.findByAlias(anyString())).thenReturn(mockCatalog);


        Catalog catalogEmptyFull = new Catalog();
        catalogEmptyFull.setId(123);
        Eir eir = new Eir();
        eir.setCatEmptyFull(catalogEmptyFull);
        Catalog catalogMovement = new Catalog();
        catalogMovement.setId(888);
        eir.setCatMovement(catalogMovement);
        eir.setContainer(mockContainer);
        eir.setTruck(new Truck());
        eir.setDriverPerson(new Person());
        eir.setCatOrigin(catalogMovement);
        eir.setBusinessUnit(businessUnit);
        eir.setSubBusinessUnit(businessUnit);
        eir.setLocalSubBusinessUnit(businessUnit);
        eir.setCatCreationOrigin(new Catalog());
        eir.setVesselProgrammingDetail(new VesselProgrammingDetail());
        when(eirRepository.findOneById(any())).thenReturn(eir);

        when(sdgStockEmptyRepository.getEirAuxByEirId(anyInt())).thenReturn(null);

        when(fgisInspectionRepository.findByEirGateInAndContainerId(any(),any())).thenReturn(new FgisInspection());

        when(eirDocumentCargoDetailRepository.findByEirAndActiveTrue(any())).thenReturn(new EirDocumentCargoDetail());

        ContainerPreassignment containerPreassignment = new ContainerPreassignment();
        containerPreassignment.setId(1);
        when(containerPreassignmentRepository.findByCargoDocumentDetailIdAndCatOriginPreassignment(any(),any())).thenReturn(List.of(containerPreassignment));
        response = truckDepartureRegisterService.register(root);
        assertEquals(null, response.getRespResult());

    }

    @Test
    void truckDepartureRegister_GateInFullNoMultipleEir() {
        root.getSdg().getInput().setReject(false);
        root.getSdg().getInput().setPassRestriction(null);
        SdgTruckDepartureRegisterOutput response;


        Container mockContainer = new Container();
        mockContainer.setId(1);
        when(chassisRepository.findChassisId(any(), any())).thenReturn(null);
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS)).thenReturn(888);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS)).thenReturn(123);
        when(containerRepository.findByContainerNumber(anyString())).thenReturn(mockContainer);
        when(containerRepository.findContainerNumberById(anyInt())).thenReturn("ABCD00004");
        Optional<User> mockUser = Optional.of(new User());
        when(userRepository.findById(anyInt())).thenReturn(mockUser);

        when(businessUnitRepository.findParentBusinessUnitId(anyInt())).thenReturn(1);
        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setBusinesUnitAlias("test");
        when(businessUnitRepository.findOneById(anyInt())).thenReturn(Optional.of(businessUnit));


        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("r_action", "true");

        when(systemRuleRepository.ruleGeneralGetOut(any(), any(), any())).thenReturn(hashMap);
        Catalog mockCatalog = new Catalog();
        when(catalogRepository.findById(any())).thenReturn(Optional.of(mockCatalog));
        when(catalogRepository.findByAlias(anyString())).thenReturn(mockCatalog);


        Catalog catalogEmptyFull = new Catalog();
        catalogEmptyFull.setId(123);
        Eir eir = new Eir();
        eir.setCatEmptyFull(catalogEmptyFull);
        Catalog catalogMovement = new Catalog();
        catalogMovement.setId(888);
        eir.setCatMovement(catalogMovement);
        eir.setContainer(mockContainer);
        eir.setTruck(new Truck());
        eir.setDriverPerson(new Person());
        eir.setCatOrigin(catalogMovement);
        eir.setBusinessUnit(businessUnit);
        eir.setSubBusinessUnit(businessUnit);
        eir.setLocalSubBusinessUnit(businessUnit);
        eir.setCatCreationOrigin(new Catalog());
        eir.setVesselProgrammingDetail(new VesselProgrammingDetail());
        when(eirRepository.findOneById(any())).thenReturn(eir);

        when(sdgStockEmptyRepository.getEirAuxByEirId(anyInt())).thenReturn(null);

        when(fgisInspectionRepository.findByEirGateInAndContainerId(any(),any())).thenReturn(new FgisInspection());

        when(eirDocumentCargoDetailRepository.findByEirAndActiveTrue(any())).thenReturn(new EirDocumentCargoDetail());

        ContainerPreassignment containerPreassignment = new ContainerPreassignment();
        containerPreassignment.setId(1);
        when(containerPreassignmentRepository.findByCargoDocumentDetailIdAndCatOriginPreassignment(any(),any())).thenReturn(List.of(containerPreassignment));

        EirDocumentCargoDetail eirDocumentCargoDetail = new EirDocumentCargoDetail();
        eirDocumentCargoDetail.setCargoDocumentDetail(new CargoDocumentDetail());
        eirDocumentCargoDetail.setEir(eir);
        when(eirDocumentCargoDetailRepository.findManyByEirId(any())).thenReturn(List.of(eirDocumentCargoDetail));

        VesselProgrammingContainer vesselProgrammingContainer = new VesselProgrammingContainer();
        when(vesselProgrammingContainerRepository.findByEirIdList(any())).thenReturn(List.of(vesselProgrammingContainer));
        response = truckDepartureRegisterService.register(root);
        assertEquals(null, response.getRespResult());

    }


    @Test
    void truckDepartureRegister_GateInEmptyNoMultipleEirWithPhotos() {
        root.getSdg().getInput().setReject(false);
        root.getSdg().getInput().setPassRestriction(null);
        SdgTruckDepartureRegisterOutput response;

        String jsonArray = "[{\"nombre\":\"foto1.jpg\",\"peso\":\"2\",\"formato\":\"jpg\",\"ubicacion\":\"/imagenes/foto1.jpg\",\"url\":\"https://example.com/foto1.jpg\",\"id\":\"12345\",\"tipoAdjunto\":1}," +
                "{\"nombre\":\"foto2.png\",\"peso\":\"1\",\"formato\":\"png\",\"ubicacion\":\"/imagenes/foto2.png\",\"url\":\"https://example.com/foto2.png\",\"id\":\"67890\",\"tipoAdjunto\":2}]";
        input.setPhotos(jsonArray);

        Container mockContainer = new Container();
        mockContainer.setId(1);
        when(chassisRepository.findChassisId(any(), any())).thenReturn(null);
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS)).thenReturn(888);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS)).thenReturn(123);
        when(containerRepository.findByContainerNumber(anyString())).thenReturn(mockContainer);
        when(containerRepository.findContainerNumberById(anyInt())).thenReturn("ABCD00004");
        Optional<User> mockUser = Optional.of(new User());
        when(userRepository.findById(anyInt())).thenReturn(mockUser);

        when(businessUnitRepository.findParentBusinessUnitId(anyInt())).thenReturn(1);
        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setBusinesUnitAlias("test");
        when(businessUnitRepository.findOneById(anyInt())).thenReturn(Optional.of(businessUnit));


        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("r_action", "true");

        when(systemRuleRepository.ruleGeneralGetOut(any(), any(), any())).thenReturn(hashMap);
        Catalog mockCatalog = new Catalog();
        when(catalogRepository.findById(any())).thenReturn(Optional.of(mockCatalog));
        when(catalogRepository.findByAlias(anyString())).thenReturn(mockCatalog);


        Catalog catalogEmptyFull = new Catalog();
        catalogEmptyFull.setId(123);
        Eir eir = new Eir();
        eir.setCatEmptyFull(catalogEmptyFull);
        Catalog catalogMovement = new Catalog();
        catalogMovement.setId(888);
        eir.setCatMovement(catalogMovement);
        eir.setContainer(mockContainer);
        eir.setTruck(new Truck());
        eir.setDriverPerson(new Person());
        eir.setCatOrigin(catalogMovement);
        eir.setBusinessUnit(businessUnit);
        eir.setSubBusinessUnit(businessUnit);
        eir.setCatCreationOrigin(new Catalog());
        eir.setLocalSubBusinessUnit(businessUnit);
        eir.setVesselProgrammingDetail(new VesselProgrammingDetail());
        when(eirRepository.findOneById(any())).thenReturn(eir);

        when(sdgStockEmptyRepository.getEirAuxByEirId(anyInt())).thenReturn(null);

        when(fgisInspectionRepository.findByEirGateInAndContainerId(any(),any())).thenReturn(new FgisInspection());

        when(eirDocumentCargoDetailRepository.findByEirAndActiveTrue(any())).thenReturn(new EirDocumentCargoDetail());

        ContainerPreassignment containerPreassignment = new ContainerPreassignment();
        containerPreassignment.setId(1);
        when(containerPreassignmentRepository.findByCargoDocumentDetailIdAndCatOriginPreassignment(any(),any())).thenReturn(List.of(containerPreassignment));

        EirDocumentCargoDetail eirDocumentCargoDetail = new EirDocumentCargoDetail();
        eirDocumentCargoDetail.setCargoDocumentDetail(new CargoDocumentDetail());
        eirDocumentCargoDetail.setEir(eir);
        when(eirDocumentCargoDetailRepository.findManyByEirId(any())).thenReturn(List.of(eirDocumentCargoDetail));

        VesselProgrammingContainer vesselProgrammingContainer = new VesselProgrammingContainer();
        when(vesselProgrammingContainerRepository.findByEirIdList(any())).thenReturn(List.of(vesselProgrammingContainer));
        eir.setId(1);
        when(eirRepository.findEirGateInByEirGateOutId(any())).thenReturn(eir);
        response = truckDepartureRegisterService.register(root);
        assertEquals(null, response.getRespResult());

    }

    @Test
    void test_returns_true_when_gate_out_assignation_is_zero() throws Exception {


        SdgTruckDepartureRegisterInput.Root inputRoot = new SdgTruckDepartureRegisterInput.Root();
        SdgTruckDepartureRegisterInput.Prefix sdg = new SdgTruckDepartureRegisterInput.Prefix();
        SdgTruckDepartureRegisterInput.Input inputData = new SdgTruckDepartureRegisterInput.Input();

        inputData.setIsForGateOutAssignation(0);
        sdg.setInput(inputData);
        inputRoot.setSdg(sdg);

        boolean result = truckDepartureRegisterService.implementAssigmentGateOut(inputRoot);

        assertTrue(result);
    }


    @Test
    void test_returns_false_when_assignment_register_returns_null() throws Exception {

        SdgTruckDepartureRegisterInput.Root inputRoot = new SdgTruckDepartureRegisterInput.Root();
        SdgTruckDepartureRegisterInput.Prefix sdg = new SdgTruckDepartureRegisterInput.Prefix();
        SdgTruckDepartureRegisterInput.Input inputData = new SdgTruckDepartureRegisterInput.Input();

        inputData.setIsForGateOutAssignation(1);
        sdg.setInput(inputData);
        inputRoot.setSdg(sdg);

        doReturn(null).when(truckDepartureRegisterService).gateoutGeneralAssignmentRegister(inputRoot, true);
        boolean result = truckDepartureRegisterService.implementAssigmentGateOut(inputRoot);


        assertFalse(result);
    }


    @Test
    void test_missing_integration_data() throws Exception {
        Map<String, Object> responseAssignGout = new HashMap<>();
        SdgTruckDepartureRegisterInput.Root inputRoot = new SdgTruckDepartureRegisterInput.Root();
        SdgTruckDepartureRegisterInput.Prefix sdg = new SdgTruckDepartureRegisterInput.Prefix();
        SdgTruckDepartureRegisterInput.Input inputData = new SdgTruckDepartureRegisterInput.Input();
        sdg.setInput(inputData);
        inputRoot.setSdg(sdg);

        Object result = truckDepartureRegisterService.yardIntegrationRule(responseAssignGout, inputRoot);

        assertEquals("Bad Response", result);

        result = truckDepartureRegisterService.yardIntegrationRule(null, inputRoot);
        assertEquals("Bad Response", result);
    }

    //@Test
    void test_process_truck_departure_when_flags_true() throws Exception {

        SdgTruckDepartureRegisterInput.Root inputRoot = new SdgTruckDepartureRegisterInput.Root();
        SdgTruckDepartureRegisterInput.Input inputData = new SdgTruckDepartureRegisterInput.Input();
        SdgTruckDepartureRegisterInput.Prefix prefixSdg = new SdgTruckDepartureRegisterInput.Prefix();
        inputData.setEirId(1);
        inputData.setSubBusinessUnitLocalId(1);
        inputData.setContainerId(1);
        inputData.setIsForGateOutAssignation(1);
        prefixSdg.setInput(inputData);
        inputRoot.setSdg(prefixSdg);
        Eir eir = new Eir();
        Catalog catMovement = new Catalog();
        catMovement.setAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS);
        eir.setCatMovement(catMovement);


        ResponseTruckDepartureRegisterBeforeYard beforeYardResponse = new ResponseTruckDepartureRegisterBeforeYard();
        beforeYardResponse.setRespResult(1);

        when(eirRepository.findOneById(1)).thenReturn(eir);

        doReturn(beforeYardResponse).when(truckDepartureRegisterBeforeYardServiceSpy).registerBeforeYard(any());
        doReturn(null).when(truckDepartureRegisterService).gateoutGeneralAssignmentRegister(inputRoot, true);
        when(truckDepartureRegisterService.implementAssigmentGateOut(inputRoot)).thenReturn(true);

        doReturn(mock(SdgTruckDepartureRegisterOutput.class)).when(truckDepartureRegisterService).register(inputRoot);

        doNothing().when(entityManager).flush();
        SdgTruckDepartureRegisterOutput result = truckDepartureRegisterService.truckDepartureRegisterProcess(inputRoot);

        verify(truckDepartureRegisterService).register(inputRoot);
        assertNotNull(result);
    }


    @Test
    void getSdyToken_invalidCredentials_throwsIOException() throws IOException {
        String loginUrl = "http://example.com/login";
        String username = "user";
        String password = "wrongPass";
        String system = "system";

        HttpURLConnection connection = mock(HttpURLConnection.class);
        when(connection.getOutputStream()).thenReturn(mock(OutputStream.class));
        when(connection.getInputStream()).thenThrow(new IOException("Invalid credentials"));

        URL url = mock(URL.class);
        when(url.openConnection()).thenReturn(connection);

        assertThrows(IOException.class, () -> truckDepartureRegisterService.getSdyToken(loginUrl, username, password, system));
    }

    @Test
    void getSdyToken_nullResponse_throwsIOException() throws IOException {
        String loginUrl = "http://example.com/login";
        String username = "user";
        String password = "pass";
        String system = "system";

        HttpURLConnection connection = mock(HttpURLConnection.class);
        when(connection.getOutputStream()).thenReturn(mock(OutputStream.class));
        when(connection.getInputStream()).thenReturn(null);

        URL url = mock(URL.class);
        when(url.openConnection()).thenReturn(connection);

        assertThrows(IOException.class, () -> truckDepartureRegisterService.getSdyToken(loginUrl, username, password, system));
    }

    @Test
    void getSdyToken_malformedResponse_throwsIOException() throws IOException {
        String loginUrl = "http://example.com/login";
        String username = "user";
        String password = "pass";
        String system = "system";

        HttpURLConnection connection = mock(HttpURLConnection.class);
        when(connection.getOutputStream()).thenReturn(mock(OutputStream.class));
        when(connection.getInputStream()).thenReturn(new ByteArrayInputStream("malformed response".getBytes(StandardCharsets.UTF_8)));

        URL url = mock(URL.class);
        when(url.openConnection()).thenReturn(connection);
        assertThrows(IOException.class, () -> truckDepartureRegisterService.getSdyToken(loginUrl, username, password, system));
    }

    @Test
    void gateoutGeneralAssignmentRegister_validInput_returnsExpectedResponse() throws Exception {
        SdgTruckDepartureRegisterInput.Root inputRoot = new SdgTruckDepartureRegisterInput.Root();
        SdgTruckDepartureRegisterInput.Input inputData = new SdgTruckDepartureRegisterInput.Input();
        inputData.setSubBusinessUnitLocalId(1);
        inputData.setEirId(2);
        inputData.setContainerId(3);
        inputData.setChassisId(4);
        inputData.setDocumentoCargaDetalleId(5);
        inputData.setPlanningDetailId(6);
        inputData.setPhotos("photos");
        inputData.setSeal1("seal1");
        inputData.setSeal2("seal2");
        inputData.setSeal3("seal3");
        inputData.setSeal4("seal4");
        inputData.setUserRegistrationId(7);
        inputData.setLanguageId(8);
        inputData.setRemarks("remarks");
        inputData.setOperationCode("operationCode");
        inputRoot.setSdg(new SdgTruckDepartureRegisterInput.Prefix());
        inputRoot.getSdg().setInput(inputData);

        HashMap<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("result_state", 1);

        when(eirRepository.gateoutGeneralAssignmentRegisterV2(any(), any(), any(),
                any(), any(), any(), any(), any(), any(), any(),
                any(), any(), any(), any(), any(), any(), any())).thenReturn(expectedResponse);

        doNothing().when(entityManager).flush();
        doNothing().when(entityManager).clear();
        Map<String, Object> response = truckDepartureRegisterService.gateoutGeneralAssignmentRegister(inputRoot, true);

        assertEquals(expectedResponse, response);
    }

    @Test
    void gateoutGeneralAssignmentRegister_nullResponse_returnsNull() throws Exception {
        SdgTruckDepartureRegisterInput.Root inputRoot = new SdgTruckDepartureRegisterInput.Root();
        SdgTruckDepartureRegisterInput.Input inputData = new SdgTruckDepartureRegisterInput.Input();
        inputData.setSubBusinessUnitLocalId(1);
        inputData.setEirId(2);
        inputData.setContainerId(3);
        inputData.setChassisId(4);
        inputData.setDocumentoCargaDetalleId(5);
        inputData.setPlanningDetailId(6);
        inputData.setPhotos("photos");
        inputData.setSeal1("seal1");
        inputData.setSeal2("seal2");
        inputData.setSeal3("seal3");
        inputData.setSeal4("seal4");
        inputData.setUserRegistrationId(7);
        inputData.setLanguageId(8);
        inputData.setRemarks("remarks");
        inputData.setOperationCode("operationCode");
        inputRoot.setSdg(new SdgTruckDepartureRegisterInput.Prefix());
        inputRoot.getSdg().setInput(inputData);

        when(eirRepository.gateoutGeneralAssignmentRegisterV2(any(), any(), any(),
                any(), any(), any(), any(), any(), any(), any(),
                any(), any(), any(), any(), any(), any(), any())).thenReturn(null);

        doNothing().when(entityManager).flush();
        Map<String, Object> response = truckDepartureRegisterService.gateoutGeneralAssignmentRegister(inputRoot, true);

        assertNull(response);
    }

    @Test
    void gateoutGeneralAssignmentRegister_avoidYardIntegration_doesNotCallYardIntegrationRule() throws Exception {
        SdgTruckDepartureRegisterInput.Root inputRoot = new SdgTruckDepartureRegisterInput.Root();
        SdgTruckDepartureRegisterInput.Input inputData = new SdgTruckDepartureRegisterInput.Input();
        inputData.setSubBusinessUnitLocalId(1);
        inputData.setEirId(2);
        inputData.setContainerId(3);
        inputData.setChassisId(4);
        inputData.setDocumentoCargaDetalleId(5);
        inputData.setPlanningDetailId(6);
        inputData.setPhotos("photos");
        inputData.setSeal1("seal1");
        inputData.setSeal2("seal2");
        inputData.setSeal3("seal3");
        inputData.setSeal4("seal4");
        inputData.setUserRegistrationId(7);
        inputData.setLanguageId(8);
        inputData.setRemarks("remarks");
        inputData.setOperationCode("operationCode");
        inputRoot.setSdg(new SdgTruckDepartureRegisterInput.Prefix());
        inputRoot.getSdg().setInput(inputData);

        HashMap<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("result_state", 1);

        when(eirRepository.gateoutGeneralAssignmentRegisterV2(any(), any(), any(),
                any(), any(), any(), any(), any(), any(), any(),
                any(), any(), any(), any(), any(), any(), any())).thenReturn(expectedResponse);

        doNothing().when(entityManager).flush();
        Map<String, Object> response = truckDepartureRegisterService.gateoutGeneralAssignmentRegister(inputRoot, true);

        verify(truckDepartureRegisterService, never()).yardIntegrationRule(any(), any());
        assertEquals(expectedResponse, response);
    }


    @Test
    void yardIntegrationRule_validResponse_returnsExpectedResult() throws IOException {
        Map<String, Object> responseAssignGout = new HashMap<>();
        responseAssignGout.put("key", "value");

        SdgTruckDepartureRegisterInput.Root inputRoot = new SdgTruckDepartureRegisterInput.Root();
        SdgTruckDepartureRegisterInput.Input inputData = new SdgTruckDepartureRegisterInput.Input();
        inputRoot.setSdg(new SdgTruckDepartureRegisterInput.Prefix());
        inputRoot.getSdg().setInput(inputData);

        Object result = truckDepartureRegisterService.yardIntegrationRule(responseAssignGout, inputRoot);

        assertNotNull(result);
    }

    @Test
    void yardIntegrationRule_nullResponseAssignGout_returnsBadResponse() throws IOException {
        SdgTruckDepartureRegisterInput.Root inputRoot = new SdgTruckDepartureRegisterInput.Root();
        SdgTruckDepartureRegisterInput.Input inputData = new SdgTruckDepartureRegisterInput.Input();
        inputRoot.setSdg(new SdgTruckDepartureRegisterInput.Prefix());
        inputRoot.getSdg().setInput(inputData);

        Object result = truckDepartureRegisterService.yardIntegrationRule(null, inputRoot);

        assertEquals("Bad Response", result);
    }

    @Test
    void yardIntegrationRule_emptyResponseAssignGout_returnsBadResponse() throws IOException {
        Map<String, Object> responseAssignGout = new HashMap<>();

        SdgTruckDepartureRegisterInput.Root inputRoot = new SdgTruckDepartureRegisterInput.Root();
        SdgTruckDepartureRegisterInput.Input inputData = new SdgTruckDepartureRegisterInput.Input();
        inputRoot.setSdg(new SdgTruckDepartureRegisterInput.Prefix());
        inputRoot.getSdg().setInput(inputData);

        Object result = truckDepartureRegisterService.yardIntegrationRule(responseAssignGout, inputRoot);

        assertEquals("Bad Response", result);
    }

    @Test
    void yardIntegrationRule_validResponseAssignGout_returnsBadResponseResult() throws IOException {
        Map<String, Object> responseAssignGout = new HashMap<>();
        responseAssignGout.put("result", "success");
        String jsonArray = "[{\"id\":1,\"container_number\":\"CNTG45445545\",\"type_product_integration\":\"sdy\"},{\"id\":2,\"name\":\"Ana\",\"type_product_integration\":\"B\"},{\"id\":3,\"name\":\"Luis\",\"type_product_integration\":\"C\"}]";

        responseAssignGout.put("integration_data", jsonArray);
        SdgTruckDepartureRegisterInput.Root inputRoot = new SdgTruckDepartureRegisterInput.Root();
        SdgTruckDepartureRegisterInput.Input inputData = new SdgTruckDepartureRegisterInput.Input();
        inputRoot.setSdg(new SdgTruckDepartureRegisterInput.Prefix());
        inputRoot.getSdg().setInput(inputData);

        doReturn("token").when(truckDepartureRegisterService).getSdyToken(any(), any(), any(), any());

        ReflectionTestUtils.setField(truckDepartureRegisterService, "gateOutUrl", "https://example.com/gateOut");

        Object result = truckDepartureRegisterService.yardIntegrationRule(responseAssignGout, inputRoot);

        assertEquals("Bad Response", result);
    }
}
