package com.maersk.sd1.sdg.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.ges.service.GESCatalogService;
import com.maersk.sd1.sdg.repository.SdgMovementInstructionRepository;
import com.maersk.sd1.sds.controller.dto.EirDeleteInput;
import com.maersk.sd1.sdy.service.CheckYardIntegrationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

class EirDeleteBeforeYardServiceTest {

    private static final Integer GATE_IN_ID = 1;
    private static final Integer GATE_OUT_ID = 2;

    @Mock
    GESCatalogService catalogService;
    @Mock
    CheckYardIntegrationService checkYardIntegrationService;
    @Mock
    ContainerRepository containerRepository;
    @Mock
    SdgMovementInstructionRepository sdgMovementInstructionRepository;
    @Mock
    EirRepository eirRepository;
    @Mock
    ContainerLocationRepository containerLocationRepository;
    @Mock
    StockEmptyRepository stockEmptyRepository;
    @Mock
    StockFullRepository stockFullRepository;
    @Mock
    MovementInstructionRepository movementInstructionRepository;
    @Mock
    WorkQueueRepository workQueueRepository;

    @InjectMocks
    EirDeleteBeforeYardService service;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    // 1. Input is null
    @Test
    void testInputIsNull() throws Exception {
        var result = service.eirDeleteBeforeYard(null);
        assertEquals(0, result.getResponseResult());
        assertEquals("", result.getResponseMessage());
    }

    // 2. EIR not found
    @Test
    void testEirNotFound() throws Exception {
        var input = mockInput();
        when(eirRepository.findOneById(anyInt())).thenReturn(null);
        assertThrows(NullPointerException.class, () -> service.eirDeleteBeforeYard(input));
    }

    // 3. EIR found, movement not gate in/out or integration is false
    @Test
    void testEirMovementNotGateInOut() throws Exception {
        var input = mockInput();
        var eir = mockEirWithMovement(999); // Not gate in/out
        when(eirRepository.findOneById(anyInt())).thenReturn(eir);
        mockCatalogServiceAliases();
        var result = service.eirDeleteBeforeYard(input);
        assertEquals(1, result.getResponseResult());
    }

    @Test
    void testEirGateInButIntegrationFalse() throws Exception {
        var input = mockInput();
        var eir = mockEirWithMovement(GATE_IN_ID);
        when(eirRepository.findOneById(anyInt())).thenReturn(eir);
        mockCatalogServiceAliases();
        when(checkYardIntegrationService.checkYardIntegration(anyString(), anyString())).thenReturn(false);
        var result = service.eirDeleteBeforeYard(input);
        assertEquals(1, result.getResponseResult());
    }

    // 4. EIR found, movement is gate out, container is null or NO_CNT/NOT_APPLICA
    @Test
    void testGateOutContainerNull() throws Exception {
        var input = mockInput();
        var eir = mockEirWithMovementAndContainer(GATE_OUT_ID, null);
        when(eirRepository.findOneById(anyInt())).thenReturn(eir);
        mockCatalogServiceAliases();
        var result = service.eirDeleteBeforeYard(input);
        assertEquals(1, result.getResponseResult());
    }

    // 5. EIR found, movement is gate out, container is NOT_APPLICA
    @Test
    void testGateOutContainerNoCnt() throws Exception {
        var input = mockInput();
        var container = mockContainer();
        container.setId(9999); // Will be set to NO_CNT by mockCatalogServiceAliases
        var dummyContainer = mockContainer();
        dummyContainer.setId(9998); // Will be set to NO_CNT by mockCatalogServiceAliases
        var eir = mockEirWithMovementAndContainer(GATE_OUT_ID, container);
        when(eirRepository.findOneById(anyInt())).thenReturn(eir);
        mockCatalogServiceAliases();
        when(containerRepository.findByContainerNumber(anyString())).thenReturn(dummyContainer);
        when(checkYardIntegrationService.checkYardIntegration(anyString(), eq("gateout"))).thenReturn(true);
        when(containerLocationRepository.findByContainerId(anyInt())).thenReturn(List.of(mockContainerLocation()));
        when(stockEmptyRepository.findByContainerAndSubBusinessUnit(anyInt(), anyInt())).thenReturn(mockStockEmpty());
        when(stockFullRepository.findByContainerAndSubBusinessUnit(anyInt(), anyInt())).thenReturn(mockStockFull());
        var result = service.eirDeleteBeforeYard(input);
        assertEquals(1, result.getResponseResult());
    }

    // 7. EIR found, movement is gate in, integration is true, container has locations, in stock (empty)
    @Test
    void testGateInIntegrationTrueWithLocationsInStockEmpty() throws Exception {
        var input = mockInput();
        var eir = mockEirWithMovementAndContainer(GATE_IN_ID, mockContainer());
        when(eirRepository.findOneById(anyInt())).thenReturn(eir);
        mockCatalogServiceAliases();
        when(checkYardIntegrationService.checkYardIntegration(anyString(), eq("gatein"))).thenReturn(true);
        when(containerLocationRepository.findVirtualLocationByBusinessUnitAndCode(any(), anyInt(), eq("Out"), eq(true)))
                .thenReturn(List.of(mockOutContainerLocation()));
        when(containerLocationRepository.findByContainerId(anyInt())).thenReturn(List.of(mockContainerLocation(), mockContainerLocation()));
        when(stockEmptyRepository.isInStockWithEir(any())).thenReturn(true);
        when(stockFullRepository.isInStockWithEir(any())).thenReturn(false);
        when(workQueueRepository.findDefaultByYardId(anyInt())).thenReturn(Optional.of(mockWorkQueue()));
        when(movementInstructionRepository.save(any())).thenReturn(null);
        when(containerLocationRepository.saveAll(any())).thenReturn(null);

        var result = service.eirDeleteBeforeYard(input);
        assertEquals(1, result.getResponseResult());
    }

    // 8. EIR found, movement is gate in, integration is true, container has locations, not in stock
    @Test
    void testGateInIntegrationTrueWithLocationsNotInStock() throws Exception {
        var input = mockInput();
        var eir = mockEirWithMovementAndContainer(GATE_IN_ID, mockContainer());
        when(eirRepository.findOneById(anyInt())).thenReturn(eir);
        mockCatalogServiceAliases();
        when(checkYardIntegrationService.checkYardIntegration(anyString(), eq("gatein"))).thenReturn(true);
        when(containerLocationRepository.findVirtualLocationByBusinessUnitAndCode(any(), anyInt(), eq("Out"), eq(true)))
                .thenReturn(List.of(mockOutContainerLocation()));
        when(containerLocationRepository.findByContainerId(anyInt())).thenReturn(List.of(mockContainerLocation(), mockContainerLocation()));
        when(stockEmptyRepository.isInStockWithEir(any())).thenReturn(false);
        when(stockFullRepository.isInStockWithEir(any())).thenReturn(false);
        when(workQueueRepository.findDefaultByYardId(anyInt())).thenReturn(Optional.of(mockWorkQueue()));

        var result = service.eirDeleteBeforeYard(input);
        assertEquals(1, result.getResponseResult());
    }

    // 9. EIR found, movement is gate in, integration is true, container has no locations
    @Test
    void testGateInIntegrationTrueNoLocations() throws Exception {
        var input = mockInput();
        var eir = mockEirWithMovementAndContainer(GATE_IN_ID, mockContainer());
        when(eirRepository.findOneById(anyInt())).thenReturn(eir);
        mockCatalogServiceAliases();
        when(checkYardIntegrationService.checkYardIntegration(anyString(), eq("gatein"))).thenReturn(true);
        when(containerLocationRepository.findVirtualLocationByBusinessUnitAndCode(any(), anyInt(), eq("Out"), eq(true)))
                .thenReturn(List.of(mockOutContainerLocation()));
        when(containerLocationRepository.findByContainerId(anyInt())).thenReturn(Collections.emptyList());
        when(workQueueRepository.findDefaultByYardId(anyInt())).thenReturn(Optional.of(mockWorkQueue()));

        var result = service.eirDeleteBeforeYard(input);
        assertEquals(1, result.getResponseResult());
    }

    // 10. Exception in finding Out location or WorkQueue
    @Test
    void testGateInIntegrationTrueNoOutLocation() throws Exception {
        var input = mockInput();
        var eir = mockEirWithMovementAndContainer(GATE_IN_ID, mockContainer());
        when(eirRepository.findOneById(anyInt())).thenReturn(eir);
        mockCatalogServiceAliases();
        when(checkYardIntegrationService.checkYardIntegration(anyString(), eq("gatein"))).thenReturn(true);
        when(containerLocationRepository.findVirtualLocationByBusinessUnitAndCode(any(), anyInt(), eq("Out"), eq(true)))
                .thenReturn(Collections.emptyList());
        assertThrows(RuntimeException.class, () -> service.eirDeleteBeforeYard(input));
    }

    @Test
    void testGateInIntegrationTrueNoWorkQueue() throws Exception {
        var input = mockInput();
        var eir = mockEirWithMovementAndContainer(GATE_IN_ID, mockContainer());
        when(eirRepository.findOneById(anyInt())).thenReturn(eir);
        mockCatalogServiceAliases();
        when(checkYardIntegrationService.checkYardIntegration(anyString(), eq("gatein"))).thenReturn(true);
        when(containerLocationRepository.findVirtualLocationByBusinessUnitAndCode(any(), anyInt(), eq("Out"), eq(true)))
                .thenReturn(List.of(mockOutContainerLocation()));
        when(containerLocationRepository.findByContainerId(anyInt())).thenReturn(List.of(mockContainerLocation()));
        when(stockEmptyRepository.isInStockWithEir(any())).thenReturn(true);
        when(stockFullRepository.isInStockWithEir(any())).thenReturn(false);
        when(workQueueRepository.findDefaultByYardId(anyInt())).thenReturn(Optional.empty());
        assertThrows(RuntimeException.class, () -> service.eirDeleteBeforeYard(input));
    }

    private EirDeleteInput.Input mockInput() {
        EirDeleteInput.Input input = new EirDeleteInput.Input();
        input.setEirId(1);
        input.setUsuarioModificacionId(100);
        return input;
    }

    private Eir mockEirWithMovement(int movementId) {
        Eir eir = new Eir();
        Catalog movement = new Catalog();
        movement.setId(movementId);
        eir.setCatMovement(movement);
        eir.setLocalSubBusinessUnit(mockBusinessUnit());
        eir.setSubBusinessUnit(mockBusinessUnit());
        return eir;
    }

    private Eir mockEirWithMovementAndContainer(int movementId, Container container) {
        Eir eir = mockEirWithMovement(movementId);
        eir.setContainer(container);
        return eir;
    }

    private BusinessUnit mockBusinessUnit() {
        BusinessUnit bu = new BusinessUnit();
        bu.setId(10);
        bu.setBusinesUnitAlias("BU_ALIAS");
        return bu;
    }

    private Container mockContainer() {
        Container c = new Container();
        c.setId(123);
        return c;
    }

    private ContainerLocation mockContainerLocation() {
        ContainerLocation cl = new ContainerLocation();
        cl.setActive(true);
        Block block = new Block();
        block.setYard(mockYard());
        cl.setBlock(block);
        Cell cell = new Cell();
        cell.setRowIndex(1);
        cl.setCell(cell);
        cl.setLevel(new Level());
        return cl;
    }

    private ContainerLocation mockOutContainerLocation() {
        return mockContainerLocation();
    }

    private Yard mockYard() {
        Yard yard = new Yard();
        yard.setId(99);
        return yard;
    }

    private StockEmpty mockStockEmpty() {
        StockEmpty se = new StockEmpty();
        Eir gateInEir = new Eir();
        gateInEir.setId(555);
        se.setGateInEir(gateInEir);
        return se;
    }

    private StockFull mockStockFull() {
        StockFull sf = new StockFull();
        Eir gateInEir = new Eir();
        gateInEir.setId(777);
        sf.setGateInEir(gateInEir);
        return sf;
    }

    private Catalog mockCatalogEmpty() {
        Catalog c = new Catalog();
        c.setId(1);
        return c;
    }

    private Catalog mockCatalogFull() {
        Catalog c = new Catalog();
        c.setId(2);
        return c;
    }

    private WorkQueue mockWorkQueue() {
        WorkQueue wq = new WorkQueue();
        wq.setId(888);
        return wq;
    }

    private void mockCatalogServiceAliases() {
        when(catalogService.getCatalogIdByAlias(anyString()))
                .thenReturn(1)
                .thenReturn(2)
                .thenReturn(3)
                .thenReturn(4)
                .thenReturn(5)
                .thenReturn(6)
                .thenReturn(7)
                .thenReturn(8)
        ;
        Container noCnt = new Container();
        noCnt.setId(9999);
        when(containerRepository.findByContainerNumber(anyString())).thenReturn(noCnt);
    }
}