package com.maersk.sd1.adm.controller;


import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.adm.dto.PersonRegisterInput;
import com.maersk.sd1.adm.dto.PersonRegisterOutput;
import com.maersk.sd1.adm.service.PersonRegisterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PersonRegisterControllerTest {

    @InjectMocks
    private PersonRegisterController personRegisterController;

    @Mock
    private PersonRegisterService personRegisterService;

    private PersonRegisterInput.Root validRequest;
    private PersonRegisterInput.Input validInput;
    private PersonRegisterOutput successOutput;

    @BeforeEach
    void setUp() {
        // Initialize valid request and input data
        validInput = new PersonRegisterInput.Input();
        validRequest = new PersonRegisterInput.Root();
        PersonRegisterInput.Prefix prefix = new PersonRegisterInput.Prefix();
        prefix.setInput(validInput);
        validRequest.setPrefix(prefix);

        // Initialize success output
        successOutput = new PersonRegisterOutput();
        successOutput.setRespEstado(1);
        successOutput.setRespMensaje("Registro realizado correctamente");
    }

    @Test
    void Given_ValidInputs_When_RegisterPerson_Then_RegisteredSuccessfully() {
        when(personRegisterService.registerPerson(any(PersonRegisterInput.Input.class))).thenReturn(successOutput);

        ResponseEntity<ResponseController<PersonRegisterOutput>> response = personRegisterController.registerPerson(validRequest);

        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().getResult().getRespEstado());

        verify(personRegisterService, times(1)).registerPerson(any(PersonRegisterInput.Input.class));
    }

    @Test
    void Given_InvalidInputs_When_RegisterPerson_Then_ExceptionResponse() {
        when(personRegisterService.registerPerson(any(PersonRegisterInput.Input.class))).thenThrow(new RuntimeException("Database error"));

        ResponseEntity<ResponseController<PersonRegisterOutput>> response = personRegisterController.registerPerson(validRequest);

        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("Database error", response.getBody().getResult().getRespMensaje());

        verify(personRegisterService, times(1)).registerPerson(any(PersonRegisterInput.Input.class));
    }

}
