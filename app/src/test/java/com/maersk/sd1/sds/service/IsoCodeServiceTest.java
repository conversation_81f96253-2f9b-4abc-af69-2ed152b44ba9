package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.IsoCodeRepository;
import com.maersk.sd1.sds.controller.dto.IsoCodeSearchOutputDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageRequest;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class IsoCodeServiceTest {

    @Mock
    private IsoCodeRepository isoCodeRepository;

    @InjectMocks
    private IsoCodeService isoCodeService;

    private List<IsoCodeSearchOutputDTO> mockResults;

    @BeforeEach
    public void setUp() {
        IsoCodeSearchOutputDTO dto1 = new IsoCodeSearchOutputDTO();
        IsoCodeSearchOutputDTO dto2 = new IsoCodeSearchOutputDTO();
        mockResults = Arrays.asList(dto1, dto2);
    }

    @Test
    void givenValidInputs_WhenSearchIsoDetails_ThenReturnExpectedResults() {
        Integer containerType = 1;
        Integer containerSize = 20;
        String isoCode = "ISO123";

        when(isoCodeRepository.searchIsoCodes(containerType, containerSize, isoCode, PageRequest.of(0,10))).thenReturn(mockResults);

        List<IsoCodeSearchOutputDTO> results = isoCodeService.searchIsoDetails(containerType, containerSize, isoCode);

        assertEquals(mockResults, results);
        verify(isoCodeRepository, times(1)).searchIsoCodes(containerType, containerSize, isoCode, PageRequest.of(0,10));
    }
}