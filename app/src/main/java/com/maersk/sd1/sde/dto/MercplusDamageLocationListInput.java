package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class MercplusDamageLocationListInput {
    @Data
    public static class Input {

        @JsonProperty("type_container")
        @NotNull(message = "The 'type_container' field cannot be null")
        private String typeContainer;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}
