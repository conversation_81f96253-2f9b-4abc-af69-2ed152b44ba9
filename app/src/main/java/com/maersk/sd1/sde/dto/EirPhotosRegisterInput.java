package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.UtilityClass;

import java.util.List;

@UtilityClass
public class EirPhotosRegisterInput {

    @Data
    public static class FotoDto {

        @JsonProperty("nombre")
        @NotNull
        private String nombre;

        @JsonProperty("peso")
        private String peso;

        @JsonProperty("formato")
        @NotNull
        private String formato;

        @JsonProperty("ubicacion")
        @NotNull
        private String ubicacion;

        @JsonProperty("url")
        private String url;

        @JsonProperty("id")
        private String id;

        @JsonProperty("tipoAdjunto")
        private Integer tipoAdjunto;

        @JsonProperty("tipoFoto")
        @Size(max = 1)
        private String tipoFoto;
    }

    @Data
    public static class Input {
        @JsonProperty("eir_id")
        @NotNull
        private Integer eirId;

        @JsonProperty("fotos")
        private List<FotoDto> fotos;

        @JsonProperty("usuario_registro_id")
        @NotNull
        private Integer usuarioRegistroId;

        @JsonProperty("equipment_category_id")
        @NotNull
        private Integer equipmentCategoryId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private EirPhotosRegisterInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private EirPhotosRegisterInput.Prefix prefix;
    }
}
