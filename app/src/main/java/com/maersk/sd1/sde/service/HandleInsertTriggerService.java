package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.common.model.DepotCredentialAppeir;
import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.common.model.EirSendAppeir;
import com.maersk.sd1.common.repository.*;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;

import static com.maersk.sd1.common.Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS;
import static com.maersk.sd1.common.Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS;

@Service
@RequiredArgsConstructor
public class HandleInsertTriggerService {

    private static final Logger logger = LogManager.getLogger(HandleInsertTriggerService.class);

    private final EirRepository eirRepository;
    private final CatalogRepository catalogRepository;
    private final ContainerRepository containerRepository;
    private final DepotCredentialAppeirRepository depotCredentialAppeirRepository;
    private final EirSendAppeirRepository eirSendAppeirRepository;

    @Transactional
    public void handleInsertTrigger(List<Integer> eirIds){

        try {

            for (Integer eirId : eirIds) {
                Eir eir = eirRepository.findById(eirId).orElse(null);
                if (eir == null) {
                    return;
                }

                Integer subBusinessUnitId = eir.getSubBusinessUnit().getId();
                Integer catMoveTypeId = eir.getCatMovement().getId();
                Integer shippingLineId = eir.getShippingLine() != null ? eir.getShippingLine().getId() : null;
                LocalDateTime truckArrival = eir.getTruckArrivalDate();
                Integer containerId = eir.getContainer() != null ? eir.getContainer().getId() : null;
                Integer catEmptyFullId = eir.getCatEmptyFull() != null ? eir.getCatEmptyFull().getId() : null;

                Integer isGateIn = catalogRepository.findIdByAliasName(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS);
                Integer isEmpty = catalogRepository.findIdByAliasName(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS);

                Container equipmentNotApplicable = containerRepository.findByContainerNumber("NOT APPLICA");
                Integer equipmentNotApplicableId = equipmentNotApplicable != null ? equipmentNotApplicable.getId() : null;

                if (Objects.equals(catMoveTypeId, isGateIn)
                        && Objects.equals(catEmptyFullId, isEmpty)
                        && containerId != null
                        && !Objects.equals(containerId, equipmentNotApplicableId)
                        && truckArrival != null
                        && ChronoUnit.DAYS.between(truckArrival.toLocalDate(), LocalDate.now()) <= 20) {

                    List<DepotCredentialAppeir> depotCredentials = depotCredentialAppeirRepository.findAll()
                            .stream()
                            .filter(dc -> Objects.equals(dc.getSubBusinessUnit().getId(), subBusinessUnitId)
                                    && Objects.equals(dc.getShippingLineId(), shippingLineId)
                                    && Boolean.TRUE.equals(dc.getSendGateIn())
                                    && Boolean.TRUE.equals(dc.getActive()))
                            .toList();

                    if (!depotCredentials.isEmpty()) {
                        DepotCredentialAppeir depotCredential = depotCredentials.getFirst();

                        EirSendAppeir eirSendAppeir = EirSendAppeir.builder()
                                .depotCredentialAppeirId(depotCredential.getId())
                                .subBusinessUnitId(subBusinessUnitId)
                                .eir(eir)
                                .flagSend('0')
                                .registrationDate(LocalDateTime.now())
                                .active(true)
                                .isNewInsert(false)
                                .appeirTrace("new0")
                                .build();

                        eirSendAppeirRepository.save(eirSendAppeir);
                    }
                }
            }

        } catch (Exception e) {
            logger.info("Error in handleInsertTrigger: " + e.getMessage());
        }
    }

}
