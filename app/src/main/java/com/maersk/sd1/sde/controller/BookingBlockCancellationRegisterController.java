package com.maersk.sd1.sde.controller;

import com.maersk.sd1.sde.dto.BookingBlockCancellationRegisterInput;
import com.maersk.sd1.sde.dto.BookingBlockCancellationRegisterOutput;
import com.maersk.sd1.sde.service.BookingBlockCancellationRegisterService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSCancelacionBloqueoBookingServiceImp")
@RequiredArgsConstructor
@Log4j2
public class BookingBlockCancellationRegisterController {

    private final BookingBlockCancellationRegisterService bookingBlockCancellationRegisterService;

    @PostMapping("/sdecancelacionBloqueoBookingRegistrar")
    public ResponseEntity<ResponseController<BookingBlockCancellationRegisterOutput>> registerCancelBlock(@RequestBody @Valid BookingBlockCancellationRegisterInput.Root request) {
        try {

            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                log.error("Invalid request received registerCancelBlock: {}", request);
                BookingBlockCancellationRegisterOutput errorOutput = new BookingBlockCancellationRegisterOutput();
                errorOutput.setRespMensaje("Invalid request received");
                errorOutput.setRespEstado(0);
                errorOutput.setRespNewId(0);
                return ResponseEntity.badRequest().body(new ResponseController<>(errorOutput));
            }

            log.info("Request received registerCancelBlock: {}", request);
            BookingBlockCancellationRegisterInput.Input input = request.getPrefix().getInput();

            BookingBlockCancellationRegisterOutput result = bookingBlockCancellationRegisterService.registerCancelBookingBlock(input);

            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            log.error("An error occurred while processing the request.", e);
            BookingBlockCancellationRegisterOutput errorOutput = new BookingBlockCancellationRegisterOutput();
            errorOutput.setRespMensaje(e.toString());
            errorOutput.setRespEstado(0);
            errorOutput.setRespNewId(0);
            return ResponseEntity.internalServerError().body(new ResponseController<>(errorOutput));
        }
    }
}