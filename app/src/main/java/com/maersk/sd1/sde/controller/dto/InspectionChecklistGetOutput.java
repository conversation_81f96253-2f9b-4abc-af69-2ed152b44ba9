package com.maersk.sd1.sde.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InspectionChecklistGetOutput {

        @JsonProperty("inspection_checklist_id")
        private Integer inspectionChecklistId;

        @JsonProperty("sub_business_unit_id")
        private Integer subBusinessUnitId;

        @JsonProperty("cat_inspection_type_id")
        private Integer catInspectionTypeId;

        @JsonProperty("order_number")
        private Integer orderNumber;

        @JsonProperty("descripcion")
        private String descripcion;
}