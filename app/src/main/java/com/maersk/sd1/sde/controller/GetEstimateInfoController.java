package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.GetEstimateInfoInput;
import com.maersk.sd1.sde.dto.GetEstimateInfoOutput;
import com.maersk.sd1.sde.service.GetEstimateInfoService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ModuleSDE/module/sde/SDEEMRServiceImp")
public class GetEstimateInfoController {
    private static final Logger logger = LogManager.getLogger(GetEstimateInfoController.class);

    private final GetEstimateInfoService getEstimateInfoService;

    @PostMapping("/sdegetEstimateInfo")
    public ResponseEntity<ResponseController<List<GetEstimateInfoOutput>>> getEstimateInfo(
            @RequestBody @Valid GetEstimateInfoInput.Root request) {
        try {
            int estimateId = request.getPrefix().getInput().getEstimateId();
            int languageId = request.getPrefix().getInput().getLanguageId();

            List<GetEstimateInfoOutput> result = getEstimateInfoService.getEstimateInfo(estimateId, languageId);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            logger.error("Error while getting estimate info", e);
            return ResponseEntity.internalServerError().body(new ResponseController<>(List.of()));
        }
    }
}