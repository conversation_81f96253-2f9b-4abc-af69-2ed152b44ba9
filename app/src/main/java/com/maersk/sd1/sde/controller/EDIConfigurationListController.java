package com.maersk.sd1.sde.controller;


import com.maersk.sd1.sde.dto.EDIConfigurationListInput;
import com.maersk.sd1.sde.dto.EDIConfigurationListOutput;
import com.maersk.sd1.sde.service.EDIConfigurationListService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSEdiServiceImp")
public class EDIConfigurationListController {

    private static final Logger logger = LogManager.getLogger(EDIConfigurationListController.class.getName());

    private final EDIConfigurationListService ediConfigurationListService;

    @PostMapping("/sdeseteoEdiCodecoListar")
    public ResponseEntity<ResponseController<EDIConfigurationListOutput>> listar(@Valid @RequestBody EDIConfigurationListInput.Root request) {
        try {
            if (request == null) {
                EDIConfigurationListOutput fallback = new EDIConfigurationListOutput();
                fallback.setTotalRegistros(List.of(List.of(0L)));
                fallback.setItems(null);
                return ResponseEntity.status(500).body(new ResponseController<>(fallback));
            }
            // call service
            EDIConfigurationListOutput output = ediConfigurationListService.listSeteoEdiCodeco(request);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            // we can return a partial fallback if needed
            EDIConfigurationListOutput fallback = new EDIConfigurationListOutput();
            fallback.setTotalRegistros(List.of(List.of(0L)));
            fallback.setItems(null);
            return ResponseEntity.status(500).body(new ResponseController<>(fallback));
        }
    }
}

