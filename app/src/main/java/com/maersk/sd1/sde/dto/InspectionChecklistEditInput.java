package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class InspectionChecklistEditInput {

    @Data
    public static class Input {

        @JsonProperty("inspection_checklist_id")
        @NotNull
        private Integer inspectionChecklistId;

        @JsonProperty("sub_business_unit_id")
        @NotNull
        private Integer subBusinessUnitId;

        @JsonProperty("cat_inspection_type_id")
        @NotNull
        private Integer catInspectionTypeId;

        @JsonProperty("order_number")
        private Integer orderNumber;

        @JsonProperty("descripcion")
        @NotNull
        @Size(max = 350)
        private String description;

        @JsonProperty("user_modification_id")
        @NotNull
        private Integer userModificationId;

        @JsonProperty("active")
        @NotNull
        private Boolean active;

        @JsonProperty("language_id")
        @NotNull
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}

