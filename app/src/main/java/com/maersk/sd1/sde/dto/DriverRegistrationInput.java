package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class DriverRegistrationInput {

    @Data
    public static class Input {

        @JsonProperty("documentoIdentidad")
        @NotNull(message = "documentoIdentidad cannot be null")
        @Size(max = 15, message = "documentoIdentidad must not exceed 15 characters")
        private String identificationDocument;

        @JsonProperty("apPaterno")
        @NotNull(message = "apPaterno cannot be null")
        @Size(max = 100, message = "apPaterno must not exceed 100 characters")
        private String firstLastName;

        // Optional, not in your payload but included for flexibility
        @JsonProperty("apMaterno")
        @Size(max = 100, message = "apMaterno must not exceed 100 characters")
        private String secondLastName;

        @JsonProperty("nombres")
        @NotNull(message = "nombres cannot be null")
        @Size(max = 100, message = "nombres must not exceed 100 characters")
        private String names;

        @JsonProperty("licenciaConducir")
        @NotNull(message = "licenciaConducir cannot be null")
        @Size(max = 50, message = "licenciaConducir must not exceed 50 characters")
        private String driversLicense;

        @JsonProperty("unidad_negocio_id")
        @NotNull(message = "unidad_negocio_id cannot be null")
        private Integer businessUnitId;

        @JsonProperty("usuario_registro_id")
        @NotNull(message = "usuario_registro_id cannot be null")
        private Integer userRegistrationId;

        @JsonProperty("correo")
        @Size(max = 300, message = "correo must not exceed 300 characters")
        private String mail;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}
