package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.controller.dto.SftpConfigDeleteInput;
import com.maersk.sd1.ges.controller.dto.SftpConfigDeleteOutput;
import com.maersk.sd1.ges.service.SftpConfigDeleteService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMSftpConfigServiceImp")
public class SftpConfigDeleteController {

    private static final Logger logger = LogManager.getLogger(SftpConfigDeleteController.class);

    private final SftpConfigDeleteService sftpConfigDeleteService;

    public SftpConfigDeleteController(SftpConfigDeleteService sftpConfigDeleteService) {
        this.sftpConfigDeleteService = sftpConfigDeleteService;
    }

    @PostMapping("/gessftpConfigEliminar")
    public ResponseEntity<ResponseController<SftpConfigDeleteOutput>> deleteSftpConfig(@RequestBody @Valid SftpConfigDeleteInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid input payload structure."));
            }
            SftpConfigDeleteInput.Input input = request.getPrefix().getInput();
            if(input.getSftpConfigId() == null){
                return ResponseEntity.badRequest().body(new ResponseController<>("sftpConfigId cannot be null."));
            }
            SftpConfigDeleteOutput output = sftpConfigDeleteService.deleteSftpConfig(
                    input.getSftpConfigId(),
                    input.getUserModificationId()
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while deleting sftp config.", e);
            SftpConfigDeleteOutput output = new SftpConfigDeleteOutput();
            output.setRespStatus(0);
            output.setRespMessage(e.getMessage());
            return ResponseEntity.internalServerError().body(new ResponseController<>(output));
        }
    }
}