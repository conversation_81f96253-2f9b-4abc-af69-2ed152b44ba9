package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class BusinessPersonalListInput {

    @Data
    public static class Input {

        @JsonProperty("company_id")
        private Integer companyId;

        @JsonProperty("person_id")
        private Integer personId;

        @JsonProperty("business_unit_id")
        private Integer businessUnitId;

        @JsonProperty("active")
        private Boolean active;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        @NotNull
        private Prefix prefix;
    }
}
