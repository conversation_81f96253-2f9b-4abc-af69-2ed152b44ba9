package com.maersk.sd1.ges.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Output DTO reflecting the OUT parameters of the stored procedure.
 */
@Data
public class AutoreportEditOutput {

    @JsonProperty("resp_status")
    private Integer respStatus;

    @JsonProperty("resp_message")
    private String respMessage;

    @JsonProperty("resp_recurrence_alias")
    private String respRecurrenceAlias;

    @JsonProperty("resp_start_format_date")
    private String respStartFormatDate;
}

