package com.maersk.sd1.adm.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class SignatureDeleteInput {

    @Data
    public static class Input {

        @JsonProperty("id")
        @NotNull(message = "signature_id cannot be null")
        private String id;

        @JsonProperty("user_id")
        private Long userId;

        @JsonProperty("person_id")
        private Integer personId;

        @JsonProperty("user_modification_id")
        @NotNull(message = "user_modification_id cannot be null")
        private Long userModificationId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("IND")
        private Prefix prefix;
    }
}
