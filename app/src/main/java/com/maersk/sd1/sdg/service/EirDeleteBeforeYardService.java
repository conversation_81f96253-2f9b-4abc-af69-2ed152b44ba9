package com.maersk.sd1.sdg.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.ges.service.GESCatalogService;
import com.maersk.sd1.sdg.dto.ResponseEirDeleteBeforeYard;
import com.maersk.sd1.sdg.repository.SdgMovementInstructionRepository;
import com.maersk.sd1.sdy.service.CheckYardIntegrationService;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;

import com.maersk.sd1.sds.controller.dto.EirDeleteInput;

import static com.maersk.sd1.common.Parameter.*;
import static com.maersk.sd1.common.Parameter.CONTAINER_NOT_APPLICA;

@Service
@RequiredArgsConstructor
public class EirDeleteBeforeYardService {

    private final GESCatalogService catalogService;

    private final  CheckYardIntegrationService checkYardIntegrationService;

    private final  ContainerRepository containerRepository;

    private final  SdgMovementInstructionRepository sdgMovementInstructionRepository;

    private final  EirRepository eirRepository;

    private final  ContainerLocationRepository containerLocationRepository;

    private final  StockEmptyRepository stockEmptyRepository;

    private final  StockFullRepository stockFullRepository;

    private final MovementInstructionRepository movementInstructionRepository;

    private final WorkQueueRepository workQueueRepository;

    // SINCE IN THIS MODULE THE METHOD IS USED DIRECTLY FROM THE CONTROLLER, WE USE THE TRANSACTIONAL ANNOTATION
    @Transactional
    public ResponseEirDeleteBeforeYard eirDeleteBeforeYard(EirDeleteInput.Input input) throws JsonProcessingException {

        ResponseEirDeleteBeforeYard response = new ResponseEirDeleteBeforeYard();

        if (input == null) {
            response.setResponseResult(0);
            response.setResponseMessage("");
            return response;
        }

        response.setResponseFlagRequirePlanApprove('0');
        var pageable = PageRequest.of(0, 1);

        Integer isGateIn = catalogService.getCatalogIdByAlias(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS);
        Integer isGateOut = catalogService.getCatalogIdByAlias(CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS);
        Integer executedMovementInstructionStatus = catalogService.getCatalogIdByAlias(EXECUTED_MOVEMENT_INSTRUCTION_STATUS_ALIAS);
        Integer cancelledMovementInstructionStatus = catalogService.getCatalogIdByAlias(CANCELLED_MOVEMENT_INSTRUCTION_STATUS_ALIAS);
        Integer isFull = catalogService.getCatalogIdByAlias(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS);
        Integer isEmpty = catalogService.getCatalogIdByAlias(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS);
        Integer catOriginPositioning = catalogService.getCatalogIdByAlias(POSITIONING_ALIAS);
        Integer instructionMovementRemoved = catalogService.getCatalogIdByAlias(REMOVED_MOVEMENT_INSTRUCTION_TYPE_ALIAS);

        Integer containerNoCntId = containerRepository.findByContainerNumber(CONTAINER_NO_CNT).getId();
        Integer containerNotApplicableId = containerRepository.findByContainerNumber(CONTAINER_NOT_APPLICA).getId();

        Eir eir = eirRepository.findOneById(Integer.parseInt(input.getEirId().toString()));
        response.setSubBusinessUnitLocalId(eir.getLocalSubBusinessUnit().getId());

        String businessUnitAlias = eir.getLocalSubBusinessUnit().getBusinesUnitAlias();
        boolean flagIntegrationYardGateOut = checkYardIntegrationService.checkYardIntegration(businessUnitAlias, "gateout");
        boolean flagIntegrationYardGateIn = checkYardIntegrationService.checkYardIntegration(businessUnitAlias, "gatein");

        if (List.of(isGateOut, isGateIn).contains(eir.getCatMovement().getId()) && (flagIntegrationYardGateIn || flagIntegrationYardGateOut)) {

            //BEGIN TRANSACTION
            List<Integer> notInList = List.of(executedMovementInstructionStatus, cancelledMovementInstructionStatus);

            //Disabling pending movements instruction
            movementInstructionRepository.disablePendingMovementInstruction(input.getEirId(), cancelledMovementInstructionStatus, notInList, input.getUsuarioModificacionId(), "TRUCK_DEPARTURE_BEFORE_EXECUTION");

            if (Objects.equals(eir.getCatMovement().getId(), isGateOut)) {
                //-- EIR has valid container AND no exist locations in yard
                if (eir.getContainer() != null && eir.getContainer().getId() != containerNoCntId && eir.getContainer().getId() != containerNotApplicableId) {
                    List<ContainerLocation> locations = containerLocationRepository.findByContainerId(eir.getContainer().getId());
                    if (!CollectionUtils.isEmpty(locations)) {
                        Integer respGateinEirId = 0;
                        if (Objects.equals(eir.getCatEmptyFull(), catalogService.getCatalogById(isEmpty))) {
                            respGateinEirId = stockEmptyRepository.findByContainerAndSubBusinessUnit(eir.getContainer().getId(), eir.getSubBusinessUnit().getId()).getGateInEir().getId();
                        }
                        if (Objects.equals(eir.getCatEmptyFull(), catalogService.getCatalogById(isFull))) {
                            respGateinEirId = stockFullRepository.findByContainerAndSubBusinessUnit(eir.getContainer().getId(), eir.getSubBusinessUnit().getId()).getGateInEir().getId();
                        }
                        response.setResponseGateinEirId(respGateinEirId);
                        response.setResponseFlagRequirePlanApprove('1');
                    }
                }
            } else {
                ContainerLocation outContainerLocation = containerLocationRepository.findVirtualLocationByBusinessUnitAndCode(pageable, eir.getLocalSubBusinessUnit().getId(), "Out", true)
                        .stream().findFirst().orElseThrow(() -> new RuntimeException("No Out location found for the business unit"));

                List<ContainerLocation> containerLocations = containerLocationRepository.findByContainerId(eir.getContainer().getId())
                        .stream().filter(cl -> Objects.equals(Boolean.TRUE, cl.getActive()))
                        .sorted(Comparator.comparing(cl -> cl.getCell().getRowIndex())).toList();

                WorkQueue defaultWorkQueue = workQueueRepository.findDefaultByYardId(outContainerLocation.getBlock().getYard().getId()).orElseThrow(() -> new RuntimeException("No default work queue found for the yard"));

                if ((Objects.equals(Boolean.TRUE, stockEmptyRepository.isInStockWithEir(input.getEirId()))
                        || Objects.equals(Boolean.TRUE, stockFullRepository.isInStockWithEir(input.getEirId())))
                        && !containerLocations.isEmpty()) {
                    ContainerLocation firstContainerLocation = containerLocations.get(0);
                    ContainerLocation secondContainerLocation = containerLocations.size() > 1 ? containerLocations.get(1) : null;

                    // Create a new MovementInstruction for the removal of the container
                    movementInstructionRepository.save(
                            MovementInstruction.builder()
                                    .container(eir.getContainer())
                                    .originYard(firstContainerLocation.getBlock().getYard())
                                    .originBlock(firstContainerLocation.getBlock())
                                    .originCell(firstContainerLocation.getCell())
                                    .originLevel(firstContainerLocation.getLevel())
                                    .origin4OYard(Optional.ofNullable(secondContainerLocation).map(ContainerLocation::getBlock).map(Block::getYard).orElse(null))
                                    .origin40Block(Optional.ofNullable(secondContainerLocation).map(ContainerLocation::getBlock).orElse(null))
                                    .origin40Cell(Optional.ofNullable(secondContainerLocation).map(ContainerLocation::getCell).orElse(null))
                                    .origin40Level(Optional.ofNullable(secondContainerLocation).map(ContainerLocation::getLevel).orElse(null))
                                    .destinationYard(outContainerLocation.getBlock().getYard())
                                    .destinationBlock(outContainerLocation.getBlock())
                                    .destinationCell(outContainerLocation.getCell())
                                    .destinationLevel(outContainerLocation.getLevel())
                                    .destination40Yard(null)
                                    .destination40Block(null)
                                    .destination40Cell(null)
                                    .destination40Level(null)
                                    .proposedDestinationYard(outContainerLocation.getBlock().getYard())
                                    .proposedDestinationBlock(outContainerLocation.getBlock())
                                    .proposedDestinationCell(outContainerLocation.getCell())
                                    .proposedDestinationLevel(outContainerLocation.getLevel())
                                    .catStatus(Catalog.builder().id(executedMovementInstructionStatus).build())
                                    .yard(outContainerLocation.getBlock().getYard())
                                    .cat(Catalog.builder().id(catOriginPositioning).build())
                                    .workQueue(defaultWorkQueue)
                                    .catOperation(Catalog.builder().id(instructionMovementRemoved).build())
                                    .truck(null)
                                    .active(true)
                                    .registrationUser(User.builder().id(input.getUsuarioModificacionId()).build())
                                    .registrationDate(LocalDateTime.now())
                                    .sequence(1)
                                    .catMovementType(Catalog.builder().id(isGateOut).build())
                                    .isImminent(false)
                                    .requiresTruck(false)
                                    .eir(null)
                                    .comment("EIR_DELETED")
                                    .build()
                    );

                    // Update container location to null
                    containerLocations.forEach(cl -> {
                        cl.setContainer(null);
                        cl.setModificationUser(User.builder().id(input.getUsuarioModificacionId()).build());
                        cl.setModificationDate(LocalDateTime.now());
                    });
                    containerLocationRepository.saveAll(containerLocations);
                }
            }

        }


        response.setResponseResult(1);
        response.setResponseMessage("");

        return response;
    }
}