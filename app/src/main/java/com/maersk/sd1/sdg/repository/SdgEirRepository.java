package com.maersk.sd1.sdg.repository;

import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.common.repository.EirRepository;
import com.maersk.sd1.sdg.dto.ContainerBookingAvailavilityDto;
import com.maersk.sd1.sdg.dto.EIRContBooDocCargo;

import com.maersk.sd1.sdg.dto.GateOutGeneralAssignmentList.TbBase;
import com.maersk.sd1.sdg.dto.TbEirChassisDto;
import com.maersk.sd1.sdg.dto.EirDetailsDTO;
import com.maersk.sd1.sdg.dto.FoundContainerDetailsDTO;
import com.maersk.sd1.sdg.dto.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface SdgEirRepository extends EirRepository {

    @Procedure(name = "Eir.truckDepartureRegisterCreateim")
    Map<String, Object> truckDepartureRegisterCreateim(
            @Param("sub_business_unit_local_id") Integer subBusinessUnitLocalId,
            @Param("eir_id") Integer eirId,
            @Param("container_id") Integer containerId,
            @Param("user_registration_id") Integer userRegistrationId
    );


    @Query("select dc.id from Eir e " +
            "join EirDocumentCargoDetail edcd ON edcd.eir.id = e.id " +
            "join CargoDocumentDetail dcd ON dcd.id = dcd.cargoDocument.id " +
            "join CargoDocument dc ON dc.id = dcd.cargoDocument.id where e.id = :eirId AND e.container.id <> :containerNoCntId AND e.catMovement.id = :isGateOut AND e.catEmptyFull.id = :isFull AND e.controlAssignmentLight = 1 AND e.active = TRUE "
    )
    Integer getDocumentoCargaIdByEirIdAndContainerIdAndIsGateOutAndIsFull(@Param("eirId") Integer eirId,
                                                                          @Param("containerNoCntId") Integer containerNoCntId,
                                                                          @Param("isGateOut") Integer isGateOut,
                                                                          @Param("isFull") Integer isFull);

    @Query(value = "SELECT sdg.fn_GetEquipmentConditionID(:eirId, :catEquipmentCategoryId,:structureMachinery,:specificTask)", nativeQuery = true)
    Integer fn_GetEquipmentConditionID(@Param("eirId") Integer eirId,
                                       @Param("catEquipmentCategoryId") Integer catEquipmentCategoryId,
                                       @Param("structureMachinery") String structureMachinery,
                                       @Param("specificTask") String specificTask);

    @Query("SELECT " +
            "new com.maersk.sd1.sdg.dto.ContainerBookingAvailavilityDto( " +
            "e.id, " +
            "e.bookingGout.id, " +
            "e.catSizeCnt.id, " +
            "CASE WHEN BD.booking.remarkRulesName = 'FLAG_TO_FLEX' THEN :isTypeContainerDry ELSE BD.catContainerType.id END, " +
            "SUM(BD.reservationQuantity), " +
            "SUM(CASE WHEN e.container.id = :containerNoCntId THEN 1 ELSE 0 END), " +
            "SUM(CASE WHEN e.container.id <> :containerNoCntId THEN 1 ELSE 0 END), " +
            "CASE WHEN BD.catContainerType.code = '1' THEN TRUE ELSE FALSE END " +
            ") " +
            "FROM BookingDetail BD " +
            "JOIN Eir e ON e.bookingGout.id = BD.booking.id " +
            "WHERE e.id IN :eirId " +
            "GROUP BY e.id, " +
            "e.bookingGout.id, " +
            "BD.booking.remarkRulesName, " +
            "e.catSizeCnt.id, " +
            "BD.catContainerType.id, " +
            "CASE WHEN BD.booking.remarkRulesName = 'FLAG_TO_FLEX' THEN :isTypeContainerDry ELSE BD.catContainerType.id END, " +
            "CASE WHEN BD.catContainerType.code = '1' THEN TRUE ELSE FALSE END ")
    List<ContainerBookingAvailavilityDto> getContainerBookingQuantity(@Param("eirId") List<Integer> eirId,
                                                                      @Param("containerNoCntId") Integer containerNoCntId,
                                                                      @Param("isTypeContainerDry") Integer isTypeContainerDry);

    @Query("SELECT new com.maersk.sd1.sdg.dto.EIRContBooDocCargo(" +
            "a.bookingGout.id, " +
            "c.bookingNumber, " +
            "c.catBookingStatus.id, " +
            "c.active, " +
            "a.businessUnit.id, " +
            "a.subBusinessUnit.id, " +
            "a.catOrigin.id, " +
            "a.controlAssignmentLight, " +
            "a.catEmptyFull.id, " +
            "a.active, " +
            "a.container.id, " +
            "b.containerNumber, " +
            "a.documentCargoGof.id, " +
            "dox.cargoDocument, " +
            "dox.active, " +
            "a.seal1, " +
            "a.seal2, " +
            "a.seal3, " +
            "a.seal4, " +
            "a.catSizeCnt.id, " +
            "a.catContainerType.id, " +
            "COALESCE(c.remarkRulesName, ''), " +
            "a.catMovement.id " +
            ") " +
            "FROM Eir a " +
            "INNER JOIN Container b ON a.container.id = b.id " +
            "LEFT JOIN Booking c ON a.bookingGout.id = c.id " +
            "LEFT JOIN CargoDocument dox ON a.documentCargoGof.id = dox.id " +
            "WHERE a.id = :eirId")
    EIRContBooDocCargo findContBooDocCargo(@Param("eirId") Integer eirId);


    @Query("SELECT e FROM Eir e " +
            "LEFT JOIN e.bookingGout bGout " +
            "LEFT JOIN e.documentCargoGof dCGof " +
            "LEFT JOIN e.eirChassis eirCh " +
            "LEFT JOIN eirCh.chassisDocumentGo chDocGo " +

            "WHERE " +
            "(e.container.id IN :valuesNoCnt OR e.container.id <> :containerNoCntId) " +
            "AND (:eirId IS NULL OR e.id = :eirId) " +
            "AND ((:truckInMin IS NULL AND :truckInMax IS NULL) " +
            "   OR e.truckArrivalDate BETWEEN :truckInMin AND :truckInMax) " +
            "AND e.localSubBusinessUnit.id = :subBusinessUnitLocalId " +
            "AND e.catMovement.id = :isGateOut " +
            "AND e.active = TRUE " +
            "AND ((e.controlAssignmentLight IN (0, 2)) " +
            "   OR (e.controlAssignmentLight = 1 AND e.catEmptyFull.id = :isFull)) " +
            "AND e.catCreationOrigin.id IN :valuesCreationOrigin " +
            "AND (:vehicle IS NULL OR e.truck.plate = :vehicle) " +
            "AND (COALESCE(:documentNumber, '') = '' " +
            "OR bGout.bookingNumber = :documentNumber " +
            "OR dCGof.cargoDocument = :documentNumber " +
            "OR chDocGo.documentChassisNumber = :documentNumber) ")
    Page<Eir> gateOutGeneralAssigmentList(@Param("valuesNoCnt") List<Integer> valuesNoCnt,
                                          @Param("containerNoCntId") Integer containerNoCntId,
                                          @Param("eirId") Integer eirId,
                                          @Param("truckInMin") LocalDateTime truckInMin,
                                          @Param("truckInMax") LocalDateTime truckInMax,
                                          @Param("subBusinessUnitLocalId") Integer subBusinessUnitLocalId,
                                          @Param("isGateOut") Integer isGateOut,
                                          @Param("isFull") Integer isFull,
                                          @Param("documentNumber") String documentNumber,
                                          @Param("valuesCreationOrigin") List<Integer> valuesCreationOrigin,
                                          @Param("vehicle") String vehicle,
                                          Pageable pageable);


    @Query(value = "SELECT e FROM Eir e " +
            "WHERE e.container.id = :containerNotApplicableId " +
            "AND e.localSubBusinessUnit.id = :subBusinessUnitLocalId " +
            "AND e.catMovement.id = :isGateOut " +
            "AND e.truck.id = :vehicleId " +
            "AND e.driverPerson.id =:driverId " +
            "AND (e.truckArrivalDate BETWEEN :startDate AND :endDate) " +
            "AND e.active = TRUE " +
            "AND e.eirChassis.chassis.id = :chassis2Id " +
            "AND e.eirChassis.active = TRUE")
    Eir findEirByChassisId(@Param("containerNotApplicableId") Integer containerNotApplicableId,
                           @Param("subBusinessUnitLocalId") Integer subBusinessUnitLocalId,
                           @Param("isGateOut") Integer isGateOut,
                           @Param("vehicleId") Integer vehicleId,
                           @Param("driverId") Integer driverId,
                           @Param("startDate") LocalDateTime startDate,
                           @Param("endDate") LocalDateTime endDate,
                           @Param("chassis2Id") Integer chassis2Id);


    @Query(value = "SELECT E.id FROM StockChassis SC " +
            "INNER JOIN Eir E ON E.eirChassis.id = SC.eirChassisGatein.id " +
            "WHERE SC.eirChassisGateout.id = :eirChassisId " +
            "AND NOT SC.eirChassisGatein.id IS NULL " +
            "AND SC.inStockChassis = FALSE " +
            "AND SC.active = TRUE " +
            "AND SC.eirChassisGatein.active = TRUE " +
            "AND E.truckDepartureDate IS NULL")
    Integer findEirIdByEirChassisId(@Param("eirChassisId") Integer eirChassisId);


    @Query(value = "SELECT e.eirChassis.chassis.id FROM Eir e " +
            "WHERE e.id = :eirId AND e.eirChassis.id = :eirChassisId " +
            "AND e.active = TRUE " +
            "AND e.eirChassis.active = TRUE")
    Integer findChassisIdByEirIdAndEirChassisId(@Param("eirId") Integer eirId, @Param("eirChassisId") Integer eirChassisId);


    @Query(value = "SELECT " +
            "new com.maersk.sd1.sdg.dto.TbEirChassisDto(" +
            "echGateIn.id," +
            "echGateOut.flagOnSiteInspection," +
            "echGateOut.flagChassisStayed " +
            ") " +

            "FROM StockChassis sc " +
            "INNER JOIN Eir echGateIn ON echGateIn.eirChassis.id = sc.eirChassisGatein.id " +
            "INNER JOIN Eir echGateOut ON echGateOut.eirChassis.id = sc.eirChassisGateout.id " +
            "WHERE sc.chassis.id = :chassisId " +
            "AND sc.eirChassisGateout.id = :eirChassisId")
    TbEirChassisDto findEirChassisGateInByChassisIdAndEirChassisId(@Param("chassisId") Integer chassisId, @Param("eirChassisId") Integer eirChassisId);

    @Procedure(name = "Eir.truckDepartureTicketNotification")
    Map<String, Object> truckDepartureTicketNotification(
            @Param("eir_notification_id") Integer eirNotificationId
    );

    @Query(value = "EXEC [sdy].[find_most_available_empty_container_gateout] " +
            "@booking_id = :bookingId, " +
            "@cat_container_size_id = :catContainerSizeId, " +
            "@cat_container_type_id = :catContainerTypeId, " +
            "@cat_container_class_id = NULL, " +
            "@pick_up_quantity = 1, " +
            "@local_sub_business_unit_id = :localSubBusinessUnitId, " +
            "@gateout_eir_id = :eirId",
            nativeQuery = true)
    List<FoundContainerDetailsDTO> findMostAvailableEmptyContainer(
            @Param("bookingId") Integer bookingId,
            @Param("catContainerSizeId") Integer catContainerSizeId,
            @Param("catContainerTypeId") Integer catContainerTypeId,
            @Param("localSubBusinessUnitId") Integer localSubBusinessUnitId,
            @Param("eirId") Integer eirId);

    @Query("SELECT new com.maersk.sd1.sdg.dto.EirDetailsDTO(" +
            "e.localSubBusinessUnit.id, " +
            "c.containerNumber, " +
            "ca.code, " +
            "c.catSize.id, " +
            "c.catContainerType.id, " +
            "b.id) " +
            "FROM Eir e " +
            "JOIN e.catOrigin ca " +
            "JOIN e.container c " +
            "JOIN e.vesselProgrammingDetail vp " +
            "JOIN Booking b ON b.vesselProgrammingDetail.id = vp.id AND b.active = true " +
            "WHERE e.id = :eirId AND e.active = true")
    List<EirDetailsDTO> findEirDetailsByEirId(@Param("eirId") Integer eirId);


    @Query(value = "SELECT S.gateInEir FROM StockFull S " +
            "WHERE S.gateOutEir.id = :eirGateOutId")
    Eir findEirGateInByEirGateOutId(Integer eirGateOutId);

    @Query(value = "SELECT E FROM Eir E " +
            "WHERE E.id = :eirId AND E.active = TRUE")
    Eir findByEirIdAndActive(@Param("eirId") Integer eirId);

    @Query(value = "select e from Eir e " +
            "inner join EirMultiple m on m.eir.id = e.id " +
            "where m.id.eirMultipleId = :eirMultipleId " +
            "and m.active = true " +
            "and e.active = true")
    List<Eir> findByEirMultipleId(@Param("eirMultipleId") Integer eirMultipleId);

    @Procedure(name = "Eir.gateGeneralAppointmentValidate")
    List<Object> gateGeneralAppointmentValidate(
            @Param("sub_business_unit_local_id") Integer subBusinessUnitLocalId,
            @Param("type_process") String typeProcess,
            @Param("exclude_shipping_line") String excludeShippingLine
    );


    @Procedure(name = "Eir.validateYardIntegration")
    Boolean validateYardIntegration(
            @Param("sub_business_unit_local_id") Integer subBusinessUnitLocalId,
            @Param("sub_business_unit_local_alias") String subBusinessUnitLocalAlias,
            @Param("type_process") String typeProcess
    );

    @Procedure(name = "Eir.gateoutGeneralAssignmentRegisterV2")
    HashMap<String, Object> gateoutGeneralAssignmentRegisterV2(
            @Param("sub_business_unit_local_id") Integer subBusinessUnitLocalId,
            @Param("eir_id") Integer eirId,
            @Param("container_id") Integer containerId,
            @Param("chassis_id") Integer chassisId,
            @Param("documento_carga_detalle_id") Integer documentoCargaDetalleId,
            @Param("planning_detail_id") Integer planningDetailId,
            @Param("photos") String photos,
            @Param("seal_1") String seal1,
            @Param("seal_2") String seal2,
            @Param("seal_3") String seal3,
            @Param("seal_4") String seal4,
            @Param("user_registration_id") Integer userRegistrationId,
            @Param("languaje_id") Integer languajeId,
            @Param("comments") String comments,
            @Param("system_rule_id") String systemRuleId,
            @Param("type_process") String typeProcess,
            @Param("operation_code") String operationCode
    );

    @Query("SELECT new com.maersk.sd1.sdg.dto.EirNotificationsDetailsDTO(" +
            "eir.businessUnit.id, " +
            "eir.subBusinessUnit.id, " +
            "eir.id, " +
            "eir.transportCompany.id, " +
            "COALESCE(con.containerNumber, ' - '), " +
            "COALESCE(cha.chassisNumber, ' - '), " +
            "eir.truckDepartureDate" +
            ") " +
            "FROM Eir eir " +
            "INNER JOIN eir.transportCompany emp " +
            "LEFT JOIN eir.container con " +
            "LEFT JOIN eir.eirChassis ech ON ech.active = true " +
            "LEFT JOIN ech.chassis cha " +
            "WHERE eir.id = :eirId " +
            "AND eir.active = true")
    EirNotificationsDetailsDTO findEirDetailsByEirNotificationIdAndEirId(@Param("eirId") Integer eirId);


    @Query("SELECT new com.maersk.sd1.sdg.dto.GateOutGeneralAssignmentList.TbBase( "
            + "b.subBusinessUnit.id, " + //subBusinessUnitId
            "b.localSubBusinessUnit.id, " //subBusinessUnitLocalId

            + "b.bookingGout.id, " + //bookingGoeId
            "COALESCE(bkx.bookingNumber, ''), " + //numeroBookingGoe
            ":isDocTypeBooking, " //catDocumentTypeGoeId

            + "b.documentCargoGof.id, " + //documentoCargaGofId
            "COALESCE(dox.cargoDocument, ''), " //documentoCargaGof
            + "CASE WHEN b.documentCargoGof IS NOT NULL THEN COALESCE(dox.catCargoDocumentType.id, :isDocTypeBl) ELSE NULL END, " //catDocumentTypeGofId
            + "dox.catDocumentCargoStatus.id, " + //catEstadoDocumentoCargaId

            "CONCAT(navex.ship, ' / ', d.voyage), " //vesselVoyage
            + "c.catOperation.id, " //catOperacionId
            + "CASE WHEN operdepotx.variable1 = 'I' THEN :isOperImport "
            + "WHEN operdepotx.variable1 = 'E' THEN :isOperExport ELSE :isOperStorage END, " //operationTypeId

            + "b.catMovement.id," + //catMovimientoId
            " b.catEmptyFull.id, " + //catEmptyFullId
            "b.catOrigin.id, " + //catProcedenciaId
            "b.id," + //eirId
            " vehx.plate, " //placa
            + "b.transportCompany.id, " + //empresaTransporteId
            "b.truckArrivalDate, " + //fechaIngresoCamion
            "b.container.id, " //contenedorId
            + "COALESCE(b.controlAssignmentLight  * 1, 0)," + //controlAsignacionLight
            " b.shippingLine.id, " + //lineaNavieraId
            "b.vesselProgrammingDetail.id, " //programacionNaveDetalleId
            + "NULL," + //fechaCutoffRetiroVacioDry
            " NULL, " + //fechaCutoffRetiroVacioReefer
            "bkx.catBookingStatus.id, " + //catEstadoBooking
            "COALESCE(b.externalDocumentNumber, ''), " //numeroDocumentoExterno

            + "b.eirChassis.id, " + //eirChassisId
            "eirchax.chassisDocumentGo.id, " + ///documentChassisGoId
            "COALESCE(eirchax.chassisAllocationControl, ''), " //chassisAllocationControl
            + "docchax.catReferenceType.id, " + //catChassisDocumentType
            "COALESCE(docchax.documentChassisNumber, ''), " //documentChassisNumber
            + "docchax.catChassisOperationType.id, " + //catChassisOperationTypeId

            "b.driverPerson.id, " //personaConductorId
            + "COALESCE(bkx.clientCompany.id, dox.shipperCompany.id), " //empresaEmbarcadorId
            + "COALESCE(bkx.shipperCompany.id, dox.consigneeCompany.id), " //empresaConsignatarioId

            + "FALSE, " + //withContainerNumer
            "FALSE, " + //findContainer

            "NULL, " + //containerFullId
            "NULL, " + //containerNumber
            "NULL, " + //containerType
            "NULL, " +//isoCode
            "NULL, " + //grade
            "NULL, " +//tara
            "NULL, " +//payload
            "NULL, " +//reeferType
            "0, "//valueReceivedWeight
            + ":isMeasureWeightKg," +//catReceivedWeightMeasureId
            " b.seal1," + //seal1
            " b.seal2, " + //seal2
            "b.seal3, " + //seal3
            "b.seal4, " //seal4
            + "FALSE, " + //findChassis
            "eirchax.chassis.id, " + //chassisId
            "chassis.chassisNumber, " + //chassisNumber
            "emp.legalName, "//ownerCompany
            + "b.observation, " + //observation
            "b.container.containerNumber, " + //preAllocatedContainer
            "COALESCE(bkx.remarkRulesName, '')" + //remarkRule
            " ) "

            + "FROM Eir b " +
            " LEFT JOIN Booking bkx ON b.bookingGout.id = bkx.id "
            + "LEFT JOIN CargoDocument dox ON b.documentCargoGof.id = dox.id AND dox.active = true "

            + "JOIN VesselProgrammingDetail c ON b.vesselProgrammingDetail.id = c.id "
            + "JOIN VesselProgramming d ON c.vesselProgramming.id = d.id "
            + "JOIN Vessel navex ON d.vessel.id = navex.id "
            + "JOIN Catalog operdepotx ON c.catOperation.id = operdepotx.id "
            + "JOIN Truck vehx ON b.truck.id = vehx.id "

            + "LEFT JOIN EirChassis eirchax ON b.eirChassis.id = eirchax.id AND eirchax.active = true "
            + "LEFT JOIN ChassisDocument docchax ON eirchax.chassisDocumentGo.id = docchax.id AND docchax.active = true "
            + "LEFT JOIN Chassis chassis ON eirchax.chassis.id = chassis.id "
            + "LEFT JOIN Company emp ON chassis.ownerCompany.id = emp.id "
            + "LEFT JOIN Container cnt ON cnt.id = b.preAllocatedContainer.id "
            + "WHERE b.container.id IN (:containerNoCntId, :containerNotApplicableId) "
            + "AND b.id = COALESCE(:eirId, b.id) "
            + "AND (:truckInMin IS NULL OR :truckInMax IS NULL OR b.truckArrivalDate BETWEEN :truckInMin AND :truckInMax) "
            + "AND b.localSubBusinessUnit.id = :subBusinessUnitLocalId "
            + "AND b.catMovement.id = :isGateOut "
            + "AND COALESCE(:apsId, '') LIKE COALESCE(b.externalDocumentNumber, '')  "
            + "AND COALESCE(:vehicle, vehx.plate) = vehx.plate "
            + "AND (:documentNumber IS NULL OR COALESCE(bkx.bookingNumber, dox.cargoDocument, docchax.documentChassisNumber, '') = :documentNumber) "
            + "AND b.catCreationOrigin.id IN (:isCreationGoGeneral, :isCreationGoLight) "
            + "AND b.controlAssignmentLight IN (0, 2) "
            + "AND b.active = true")
    List<TbBase> getFilteredResults1(
            @Param("containerNoCntId") Integer containerNoCntId,
            @Param("containerNotApplicableId") Integer containerNotApplicableId,
            @Param("eirId") Integer eirId,
            @Param("truckInMin") LocalDateTime truckInMin,
            @Param("truckInMax") LocalDateTime truckInMax,
            @Param("subBusinessUnitLocalId") Integer subBusinessUnitLocalId,
            @Param("isGateOut") Integer isGateOut,
            @Param("apsId") String apsId,
            @Param("vehicle") String vehicle,
            @Param("documentNumber") String documentNumber,
            @Param("isDocTypeBl") Integer isDocTypeBl,
            @Param("isOperImport") Integer isOperImport,
            @Param("isOperExport") Integer isOperExport,
            @Param("isOperStorage") Integer isOperStorage,
            @Param("isMeasureWeightKg") Integer isMeasureWeightKg,
            @Param("isCreationGoGeneral") Integer isCreationGoGeneral,
            @Param("isCreationGoLight") Integer isCreationGoLight,
            @Param("isDocTypeBooking") Integer isDocTypeBooking
    );

    @Query("SELECT  new com.maersk.sd1.sdg.dto.GateOutGeneralAssignmentList.TbBase("
            + "b.subBusinessUnit.id, " //subBusinessUnitId
            + "b.localSubBusinessUnit.id, " //subBusinessUnitLocalId

            + "NULL, " + //bookingGoeId
            "NULL," + //numeroBookingGoe
            " NULL, " //catDocumentTypeGoeId

            + "dox.id, " //documentoCargaGofId
            + "COALESCE(dox.cargoDocument, ''), " //documentoCargaGof
            + "COALESCE(dox.catCargoDocumentType.id, :isDocTypeBl), " //catDocumentTypeGofId
            + "dox.catDocumentCargoStatus.id, " //catEstadoDocumentoCargaId

            + "CONCAT(navex.ship, ' / ', d.voyage), " //vesselVoyage
            + "c.catOperation.id, " //catOperacionId
            + "CASE WHEN operdepotx.variable1 = 'I' THEN :isOperImport "
            + "WHEN operdepotx.variable1 = 'E' THEN :isOperExport ELSE :isOperStorage END, " //operationTypeId

            + "b.catMovement.id, " //catMovimientoId
            + "b.catEmptyFull.id, "//catEmptyFullId
            + "b.catOrigin.id, " //catProcedenciaId
            + "b.id, " //eirId
            + "vehx.plate, " //placa
            + "b.transportCompany.id, " //empresaTransporteId
            + "b.truckArrivalDate, " //fechaIngresoCamion
            + "b.container.id, " //contenedorId
            + "COALESCE(b.controlAssignmentLight  * 1, 0), " //controlAsignacionLight
            + "b.shippingLine.id, " //lineaNavieraId
            + "b.vesselProgrammingDetail.id, " //programacionNaveDetalleId
            + "NULL, " + //fechaCutoffRetiroVacioDry
            "NULL, " + //fechaCutoffRetiroVacioReefer
            "NULL, " //catEstadoBooking
            + "COALESCE(b.externalDocumentNumber, ''), " //numeroDocumentoExterno

            + "b.eirChassis.id, " //eirChassisId
            + "eirchax.chassisDocumentGo.id, " //documentChassisGoId
            + "COALESCE(eirchax.chassisAllocationControl, ''), " //chassisAllocationControl
            + "docchax.catReferenceType.id, " //catChassisDocumentType
            + "COALESCE(docchax.documentChassisNumber, ''), " //documentChassisNumber
            + "docchax.catChassisOperationType.id, " //catChassisOperationTypeId

            + "b.driverPerson.id, " //personaConductorId
            + "dox.shipperCompany.id, " //empresaEmbarcadorId
            + "dox.consigneeCompany.id, " //empresaConsignatarioId

            + "TRUE," + //withContainerNumer
            " FALSE, " //findContainer

            + "b.container.id, " //containerFullId
            + "NULL, " + //containerNumber
            "NULL, " + //containerType
            "NULL, " + //isoCode
            "NULL, " + //grade
            "NULL, " + //tara
            "NULL, " + //payload
            "NULL, " //reeferType
            + "COALESCE(CAST(dodx.receivedWeight AS INTEGER), 0), " +//valueReceivedWeight
            ":isMeasureWeightKg, " //catReceivedWeightMeasureId
            + "b.seal1, " + //seal1
            "b.seal2, " +//seal2
            "b.seal3, " +//seal3
            "b.seal4, "//seal4
            + "FALSE, " //findChassis
            + "eirchax.chassis.id, " //chassisId
            + "chassis.chassisNumber, " //chassisNumber
            + "emp.legalName, " //ownerCompany
            + "b.observation, " //observation
            + "cnt.containerNumber," + //preAllocatedContainer
            " ''  )" //remarkRule
            + "FROM Eir b "
            + "JOIN VesselProgrammingDetail c ON b.vesselProgrammingDetail.id = c.id "
            + "JOIN VesselProgramming d ON c.vesselProgramming.id = d.id "
            + "JOIN Vessel navex ON d.vessel.id = navex.id "
            + "JOIN Catalog operdepotx ON c.catOperation.id = operdepotx.id "
            + "JOIN Truck vehx ON b.truck.id = vehx.id "
            + "JOIN EirDocumentCargoDetail eirdocx ON b.id = eirdocx.eir.id "
            + "JOIN CargoDocumentDetail dodx ON eirdocx.cargoDocumentDetail.id = dodx.id "
            + "JOIN CargoDocument dox ON dodx.cargoDocument.id = dox.id "
            + "LEFT JOIN EirChassis eirchax ON b.eirChassis.id = eirchax.id AND eirchax.active = TRUE "
            + "LEFT JOIN ChassisDocument docchax ON eirchax.chassisDocumentGo.id = docchax.id AND docchax.active = TRUE "
            + "LEFT JOIN Chassis chassis ON eirchax.chassis.id = chassis.id "
            + "LEFT JOIN Company emp ON chassis.ownerCompany.id = emp.id "
            + "LEFT JOIN Container cnt ON cnt.id = b.preAllocatedContainer.id "
            + "WHERE b.container.id <> :containerNoCntId "
            + "AND b.id = COALESCE(:eirId, b.id) "
            + "AND (:truckInMin IS NULL OR :truckInMax IS NULL OR b.truckArrivalDate BETWEEN :truckInMin AND :truckInMax) "
            + "AND b.localSubBusinessUnit.id = :subBusinessUnitLocalId "
            + "AND b.catMovement.id = :isGateOut "
            + "AND b.catEmptyFull.id = :isFull "
            + "AND COALESCE(:vehicle, vehx.plate) = vehx.plate "
            + "AND (:documentNumber IS NULL OR COALESCE(dox.cargoDocument, docchax.documentChassisNumber, '') = :documentNumber) "
            + "AND b.catCreationOrigin.id = :isCreationGoGeneral "
            + "AND b.controlAssignmentLight = 1 "
            + "AND b.active = TRUE")
    List<TbBase> getFilteredResults2(
            @Param("containerNoCntId") Integer containerNoCntId,
            @Param("eirId") Integer eirId,
            @Param("truckInMin") LocalDateTime truckInMin,
            @Param("truckInMax") LocalDateTime truckInMax,
            @Param("subBusinessUnitLocalId") Integer subBusinessUnitLocalId,
            @Param("isGateOut") Integer isGateOut,
            @Param("isFull") Integer isFull,
            @Param("vehicle") String vehicle,
            @Param("documentNumber") String documentNumber,
            @Param("isDocTypeBl") Integer isDocTypeBl,
            @Param("isOperImport") Integer isOperImport,
            @Param("isOperExport") Integer isOperExport,
            @Param("isOperStorage") Integer isOperStorage,
            @Param("isMeasureWeightKg") Integer isMeasureWeightKg,
            @Param("isCreationGoGeneral") Integer isCreationGoGeneral
    );

    @Query(value = "select e from Eir e where e.bookingGout.id = :bookingGoeId")
    List<Eir> findByBookingGout(Integer bookingGoeId);
}
