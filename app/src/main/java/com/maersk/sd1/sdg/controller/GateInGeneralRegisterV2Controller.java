package com.maersk.sd1.sdg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdg.controller.dto.GateInGeneralRegisterV2Input;
import com.maersk.sd1.sdg.controller.dto.GateInGeneralRegisterV2Output;
import com.maersk.sd1.sdg.service.GateInGeneralRegisterV2Service;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController()
@RequiredArgsConstructor
@RequestMapping("/ModuleSDG/module/sdg/SDGGateInServiceImp")
public class GateInGeneralRegisterV2Controller {

    private final GateInGeneralRegisterV2Service service;
    /**
     * Endpoint to register a gate-in transaction.
     * Validates the request and processes the gate-in registration.
     *
     * @param request The request containing gate-in details.
     * @return ResponseEntity with the result of the registration.
     */
    @PostMapping(value = "/sdggateinGeneralRegisterV2")
    public ResponseEntity<ResponseController<GateInGeneralRegisterV2Output.Output>> sdggateinGeneralRegisterV2(@RequestBody GateInGeneralRegisterV2Input.Root request) {
        if (request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
            return ResponseEntity.badRequest().build();
        } else {
            GateInGeneralRegisterV2Input.Input input = request.getPrefix().getInput();
            if (input.getSubBusinessUnitLocalId() == null || input.getEquipment() == null ||
                    input.getDriverId() == null || input.getVehicleId() == null ||
                    input.getTruckCompanyId() == null || input.getUserRegistrationId() == null) {
                return ResponseEntity.badRequest().build();
            }
        }

        try {
            GateInGeneralRegisterV2Output.Output gateInOutput = service.registerGateInTransactional(request);
            if (gateInOutput.getResult().getResultState().equals(1)) {
                service.handleIntegrations(request.getPrefix().getInput(), gateInOutput);
            }

            if(!gateInOutput.getResponse().isEmpty()) {

                List<Integer> eirIds = gateInOutput.getResponse().stream().map(GateInGeneralRegisterV2Output.Response::getEirId).toList();

                service.handleInsertTrigger(eirIds);

            }

            return ResponseEntity.ok(new ResponseController<>(gateInOutput));
        } catch (Exception e) {
            return ResponseEntity.ok(new ResponseController<>(GateInGeneralRegisterV2Output.Output.builder()
                    .result(GateInGeneralRegisterV2Output.Result.builder()
                            .resultState(2)
                            .eirId("0")
                            .resultMessage(e.getMessage())
                            .build())
                    .build()));
        }
    }
}
