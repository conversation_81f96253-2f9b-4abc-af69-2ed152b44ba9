package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.maersk.sd1.common.serializer.ObjectArrayListSerializer;
import lombok.*;

import java.util.List;

public class TruckDepartureListOutput {

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    @Setter
    @ToString
    public static class ListResult {
        private Integer eir;
        private String fechaEIR;
        private String placa;
        private String contenedor;
        private String documento;
        private String movimiento;
        private String fechaInspeccion;
        private String conductor;
        private String licenciaConducir;
        private Integer conductorId;
        private String conductorNombreCompleto;
        private Integer inspectorId;
        private String inspector;
        private Integer truckOutDateManual;
        private Integer contentCode;
        private String content;
        private String sd1MovementType;
        private String sealsRequired;
        private String chassisNumber;
        private String chassisDocument;
        private String equipmentType;
        private Integer inspectorChassisId;
        private String inspectorChassis;
        private String inspectorChassisSignature;
        private Integer eirChassisId;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ListCount {
        private Integer total;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @JsonSerialize(using = ObjectArrayListSerializer.class)
    public static class Output {
        private List<ListResult> listResult;
        private ListCount listCount;
    }
}
