package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class ResponseTruckDepartureGetDto {
    private String eir;
    private Integer truckCompanyId;
    private String truckComanyName;
    private String truckInDate;
    private String movementId;
    private String movement;
    private String truckLicensePlate;
    private String driverLicense;
    private String driverName;
    private String equipmentNumber;
    private String equipmentType;
    private String equipmentReferenceNumber;
    private String shippingLineName;
    private Integer shippingLineId;
    private Integer shipperId;
    private String shipperName;
    private String consignee;
    private Integer consigneeId;
    private Integer eirChassisId;
    private String chassisNumber;
    private Integer chassisDocumentReferenceId;
    private String chassisDocumentNumber;
    private String chassisDocumentReferenceName;
    private String chassisTypeName;
    private Integer chassisTypeId;
    private Integer ownerId;
    private String ownerName;
    private String equipmentGrade;
    private String structureCondition;
    private String machineryCondition;
    private String structureConditionChassis;
    private Integer isDummy;
    private String isReefer;
    private String equipmentSize;
    private String seal1;
    private String seal2;
    private String seal3;
    private String seal4;
    private String movementLarge;
    private String inputChassisNumber;
    private Boolean findChassis;
    private Boolean findContainer;
    private String equipmentSizeType;
    private String documentChassisId;
    private Integer catProcedenciaId;
    private String chassisAvailabilityType;
    private String containerAvailabilityType;
    private String accelerateProgramNumber;


}
