package com.maersk.sd1.sdg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.exception.SD1ControlledException;
import com.maersk.sd1.sde.service.HandleInsertTriggerService;
import com.maersk.sd1.sdg.controller.dto.GateOutGeneralRegisterInput;
import com.maersk.sd1.sdg.controller.dto.GateOutGeneralRegisterOutput;
import com.maersk.sd1.sdg.service.GateOutGeneralRegisterService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ModuleSDG/module/sdg/SDGGateOutServiceImp")
public class GateOutGeneralRegisterController {

    private static final Logger logger = LogManager.getLogger(GateOutGeneralRegisterController.class.getName());

    private final GateOutGeneralRegisterService service;
    private final HandleInsertTriggerService handleInsertTriggerService;


    @PostMapping("/sdggateoutGeneralRegister")
    public ResponseEntity<ResponseController<GateOutGeneralRegisterOutput>> gateOutGeneralRegister(@RequestBody GateOutGeneralRegisterInput.Root request) {
        if (request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
            return ResponseEntity.badRequest().build();
        } else {
            GateOutGeneralRegisterInput.Input input = request.getPrefix().getInput();
            if (input.getSubBusinessUnitLocalId() == null ||
                    input.getDriverId() == null || input.getVehicleId() == null ||
                    input.getTruckCompanyId() == null || input.getUserRegistrationId() == null) {
                return ResponseEntity.badRequest().build();
            }
        }

        GateOutGeneralRegisterInput.Input input = request.getPrefix().getInput();
        try {
            GateOutGeneralRegisterOutput gateOutOutput = service.gateOutGeneralRegisterTransactional(request);
            if (gateOutOutput != null && gateOutOutput.getResultState().equals(1)) {
                service.handleIntegrations(input, gateOutOutput);
            }

            if(gateOutOutput != null && gateOutOutput.getResultEirListId() != null) {
                try {
                    JSONArray jsonArray = new JSONArray(gateOutOutput.getResultEirListId());
                    List<Integer> eirIds = new ArrayList<>();

                    for (int i = 0; i < jsonArray.length(); i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        if (jsonObject.has("eir_id")) {
                            eirIds.add(jsonObject.getInt("eir_id"));
                        }
                    }

                    if (!eirIds.isEmpty()) {
                        handleInsertTriggerService.handleInsertTrigger(eirIds);
                    }
                } catch (Exception e) {
                    logger.info("Error parsing EIR IDs: {}" ,e.getMessage());
                }
            }

            return ResponseEntity.ok(new ResponseController<>(gateOutOutput));
        } catch (Exception e) {
            return ResponseEntity.ok(new ResponseController<>(GateOutGeneralRegisterOutput.builder()
                    .resultState(e instanceof SD1ControlledException ? 2 : 0)
                    .resultMessage(e.getMessage())
                    .build()));
        }
    }
}
