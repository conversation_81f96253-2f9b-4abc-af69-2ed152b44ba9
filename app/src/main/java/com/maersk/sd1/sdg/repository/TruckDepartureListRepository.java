package com.maersk.sd1.sdg.repository;

import java.util.List;

import com.maersk.sd1.sdg.dto.TruckDepartureListEir;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.maersk.sd1.common.repository.EirRepository;

public interface TruckDepartureListRepository extends EirRepository {

    @Query(value = """
            SELECT DISTINCT
            new com.maersk.sd1.sdg.dto.TruckDepartureListEir(
            eir.id,
            eir.truckArrivalDate,
            CASE WHEN cnt.id <> :notApplicableContainerId THEN cnt.containerNumber ELSE '' END,
            trk.plate,
            CONCAT(UPPER(TRIM(drv.names)), ' ', UPPER(TRIM(drv.firstLastName))),
            drv.driversLicense,
            eir.catMovement.id,
            eir.catOrigin.id,
            COALESCE(eir.dateRevision, eir.inspectionDate),
            COALESCE(dcg.cargoDocument, COALESCE(edcd.documentCargoReference, bkg.bookingNumber)),
            COALESCE(eir.controlRevision, 0),
            drv.id,
            eir.inspectorPerson.id,
            eir.catCreationOrigin.id,
            eir.catEmptyFull.id,
            eir.catEmptyFull.longDescription,
            chss.chassisNumber,
            COALESCE(dchs.documentChassisNumber, dcch.documentChassisNumber),
            '',
            echs.inspectorPersonChassis.id,
            echs.urlSignatureChassisInspector,
            echs.id,
            eir.catSizeCnt.id,
            eir.catContainerType.id
            )
            FROM
            Eir eir
            INNER JOIN StockEmpty smty ON (smty.gateInEir.id = eir.id)
            INNER JOIN eir.truck trk
            LEFT JOIN eir.container cnt
            INNER JOIN eir.driverPerson drv
            LEFT JOIN EirDocumentCargoDetail edcd
                ON edcd.id = (
                    SELECT MAX(sub.id) FROM EirDocumentCargoDetail sub WHERE sub.eir.id = eir.id
                )
            LEFT JOIN eir.documentCargoGof dcg
            LEFT JOIN eir.bookingGout bkg
            INNER JOIN eir.catEmptyFull catCtg
            LEFT JOIN eir.eirChassis echs
            LEFT JOIN echs.chassis chss
            LEFT JOIN echs.chassisDocumentDetail dchd
            LEFT JOIN dchd.chassisDocument dcch
            LEFT JOIN echs.chassisDocumentGo dchs
            WHERE
            eir.localSubBusinessUnit.id = :localSubBusinessUnitId
            AND eir.catMovement.id = :gateInMovementCatalogId
            AND eir.catEmptyFull.id = :emptyCategoryCatalogId
            AND eir.active = true
            AND smty.inStock = true
            AND eir.truckDepartureDate IS NULL
            """)
    List<TruckDepartureListEir> truckDepartureListEmptyGateIns(@Param("localSubBusinessUnitId") Integer localSubBusinessUnitId, @Param("gateInMovementCatalogId") Integer gateInMovementCatalogId, @Param("emptyCategoryCatalogId") Integer emptyCategoryCatalogId, @Param("notApplicableContainerId") Integer notApplicableContainerId);

    @Query(value = """
            SELECT DISTINCT
            new com.maersk.sd1.sdg.dto.TruckDepartureListEir(
            eir.id,
            eir.truckArrivalDate,
            CASE WHEN cnt.id <> :notApplicableContainerId THEN cnt.containerNumber ELSE '' END,
            trk.plate,
            CONCAT(UPPER(TRIM(drv.names)), ' ', UPPER(TRIM(drv.firstLastName))),
            drv.driversLicense,
            eir.catMovement.id,
            eir.catOrigin.id,
            COALESCE(eir.dateRevision, eir.inspectionDate),
            COALESCE(dcg.cargoDocument, COALESCE(edcd.documentCargoReference, bkg.bookingNumber)),
            COALESCE(eir.controlRevision, 0),
            drv.id,
            eir.inspectorPerson.id,
            eir.catCreationOrigin.id,
            eir.catEmptyFull.id,
            eir.catEmptyFull.longDescription,
            chss.chassisNumber,
            COALESCE(dchs.documentChassisNumber, dcch.documentChassisNumber),
            '',
            echs.inspectorPersonChassis.id,
            echs.urlSignatureChassisInspector,
            echs.id,
            eir.catSizeCnt.id,
            eir.catContainerType.id
            )
            FROM
            Eir eir
            INNER JOIN StockFull sfll ON (sfll.gateInEir.id = eir.id)
            INNER JOIN eir.truck trk
            INNER JOIN eir.container cnt
            INNER JOIN eir.driverPerson drv 
            LEFT JOIN EirDocumentCargoDetail edcd
                ON edcd.id = (
                    SELECT MAX(sub.id) FROM EirDocumentCargoDetail sub WHERE sub.eir.id = eir.id
                )
            LEFT JOIN eir.documentCargoGof dcg
            LEFT JOIN eir.bookingGout bkg
            INNER JOIN eir.catEmptyFull catCtg
            LEFT JOIN eir.eirChassis echs
            LEFT JOIN echs.chassis chss
            LEFT JOIN echs.chassisDocumentDetail dchd
            LEFT JOIN dchd.chassisDocument dchs
            LEFT JOIN echs.chassisDocumentGo dcch
            WHERE
            eir.localSubBusinessUnit.id = :localSubBusinessUnitId
            AND eir.catMovement.id = :gateInMovementCatalogId
            AND eir.catEmptyFull.id = :fullCategoryCatalogId
            AND eir.active = true
            AND sfll.inStock = true
            AND eir.truckDepartureDate IS NULL
            """)
    List<TruckDepartureListEir> truckDepartureListFullGateIns(@Param("localSubBusinessUnitId") Integer localSubBusinessUnitId, @Param("gateInMovementCatalogId") Integer gateInMovementCatalogId, @Param("fullCategoryCatalogId") Integer fullCategoryCatalogId, @Param("notApplicableContainerId") Integer notApplicableContainerId);

    @Query(value = """
            SELECT DISTINCT
            new com.maersk.sd1.sdg.dto.TruckDepartureListEir(
            eir.id,
            eir.truckArrivalDate,
            CASE WHEN cnt.id <> :notApplicableContainerId THEN cnt.containerNumber ELSE '' END,
            trk.plate,
            CONCAT(UPPER(TRIM(drv.names)), ' ', UPPER(TRIM(drv.firstLastName))),
            drv.driversLicense,
            eir.catMovement.id,
            eir.catOrigin.id,
            COALESCE(eir.dateGateOutInspection, eir.dateRevision),
            COALESCE(dcc.cargoDocument, COALESCE(dcg.cargoDocument, bkg.bookingNumber)),
            (CAST(0 AS java.lang.Short)),
            eir.driverPerson.id,
            eir.inspectorPerson.id,
            eir.catCreationOrigin.id,
            catCtg.id,
            catCtg.longDescription,
            chss.chassisNumber,
            COALESCE(dcch.documentChassisNumber, chdc.documentChassisNumber),
            '',
            echs.inspectorPersonChassis.id,
            echs.urlSignatureChassisInspector,
            echs.id,
            eir.catSizeCnt.id,
            eir.catContainerType.id
            )
            FROM
            Eir eir
            INNER JOIN eir.truck trk
            LEFT JOIN eir.container cnt
            INNER JOIN eir.driverPerson drv
            LEFT JOIN EirDocumentCargoDetail edcd
              ON edcd.id = (
                SELECT MAX(sub.id) FROM EirDocumentCargoDetail sub WHERE sub.eir.id = eir.id
              )
            LEFT JOIN edcd.cargoDocumentDetail dcd
            LEFT JOIN dcd.cargoDocument dcg
            LEFT JOIN eir.documentCargoGof dcc
            LEFT JOIN eir.bookingGout bkg
            INNER JOIN eir.catEmptyFull catCtg
            LEFT JOIN eir.eirChassis echs
            LEFT JOIN echs.chassis chss
            LEFT JOIN echs.chassisDocumentDetail dchd
            LEFT JOIN dchd.chassisDocument dcch
            LEFT JOIN echs.chassisDocumentGo chdc
            WHERE
            eir.localSubBusinessUnit.id = :localSubBusinessUnitId
            AND eir.catMovement.id = :gateOutMovementCatalogId
            AND cnt.id <> :dummyContainerId
            AND ((eir.catCreationOrigin.id = :lighGateOutCatalogId
            AND eir.controlAssignmentLight = 3)
            OR eir.catCreationOrigin.id <> :lighGateOutCatalogId)
            AND eir.active = true
            AND eir.truckDepartureDate IS NULL
            """)
    List<TruckDepartureListEir> truckDepartureListAssignedGateOut(@Param("localSubBusinessUnitId") Integer localSubBusinessUnitId, @Param("gateOutMovementCatalogId") Integer gateOutMovementCatalogId, @Param("lighGateOutCatalogId") Integer lighGateOutCatalogId, @Param("dummyContainerId") Integer dummyContainerId, @Param("notApplicableContainerId") Integer notApplicableContainerId);

    @Query(value = """
            SELECT DISTINCT 
            new com.maersk.sd1.sdg.dto.TruckDepartureListEir(
            eir.id,
            eir.truckArrivalDate,
            CASE WHEN cnt.id <> :notApplicableContainerId THEN cnt.containerNumber ELSE '' END,
            trk.plate,
            CONCAT(UPPER(TRIM(drv.names)), ' ', UPPER(TRIM(drv.firstLastName))),
            drv.driversLicense,
            eir.catMovement.id,
            eir.catOrigin.id,
            COALESCE(eir.dateGateOutInspection, eir.dateRevision),
            COALESCE(dcc.cargoDocument, COALESCE(dcg.cargoDocument, bkg.bookingNumber)),
            (CAST(0 AS java.lang.Short)),
            drv.id,
            eir.inspectorPerson.id,
            eir.catCreationOrigin.id,
            catCtg.id,
            catCtg.longDescription,
            chss.chassisNumber,
            COALESCE(dcch.documentChassisNumber, chdc.documentChassisNumber),
            '',
            echs.inspectorPersonChassis.id,
            echs.urlSignatureChassisInspector,
            echs.id,
            eir.catSizeCnt.id,
            eir.catContainerType.id
            ) 
            FROM
            Eir eir
            INNER JOIN eir.truck trk
            LEFT JOIN eir.container cnt
            INNER JOIN eir.driverPerson drv
            LEFT JOIN EirDocumentCargoDetail edcd
              ON edcd.id = (
                SELECT MAX(sub.id) FROM EirDocumentCargoDetail sub WHERE sub.eir.id = eir.id
              )
            LEFT JOIN edcd.cargoDocumentDetail dcd
            LEFT JOIN dcd.cargoDocument dcg 
            LEFT JOIN eir.documentCargoGof dcc
            LEFT JOIN eir.bookingGout bkg
            INNER JOIN eir.catEmptyFull catCtg
            LEFT JOIN eir.eirChassis echs
            LEFT JOIN echs.chassis chss
            LEFT JOIN echs.chassisDocumentDetail dchd
            LEFT JOIN dchd.chassisDocument dcch
            LEFT JOIN echs.chassisDocumentGo chdc
            WHERE
            eir.localSubBusinessUnit.id = :localSubBusinessUnitId
            AND eir.catMovement.id = :gateOutMovementCatalogId
            AND cnt.id = :dummyContainerId
            AND (eir.controlAssignmentLight IS NULL OR eir.controlAssignmentLight = 0)
            AND eir.active = true
            AND eir.truckDepartureDate IS NULL
            """)
    List<TruckDepartureListEir> truckDepartureListUnassignedGateOut(@Param("localSubBusinessUnitId") Integer localSubBusinessUnitId, @Param("gateOutMovementCatalogId") Integer gateOutMovementCatalogId, @Param("dummyContainerId") Integer dummyContainerId, @Param("notApplicableContainerId") Integer notApplicableContainerId);

    @Query(value = """
            SELECT 
            new com.maersk.sd1.sdg.dto.TruckDepartureListEir(
            eir.id,
            eir.truckArrivalDate,
            CASE WHEN cnt.id <> :notApplicableContainerId THEN cnt.containerNumber ELSE '' END,
            trk.plate,
            CONCAT(UPPER(TRIM(drv.names)), ' ', UPPER(TRIM(drv.firstLastName))),
            drv.driversLicense,
            eir.catMovement.id,
            eir.catOrigin.id,
            COALESCE(eir.dateRevision, eir.inspectionDate),
            COALESCE(dcg.cargoDocument, COALESCE(edcd.documentCargoReference, bkg.bookingNumber)),
            COALESCE(eir.controlRevision, 0),
            drv.id,
            eir.inspectorPerson.id,
            eir.catCreationOrigin.id,
            catCtg.id,
            catCtg.longDescription,
            chss.chassisNumber,
            COALESCE(dchs.documentChassisNumber, dcch.documentChassisNumber),
            '',
            echs.inspectorPersonChassis.id,
            echs.urlSignatureChassisInspector,
            echs.id,
            eir.catSizeCnt.id,
            eir.catContainerType.id
            )
            FROM
            Eir eir
            INNER JOIN eir.truck trk
            INNER JOIN eir.container cnt
            INNER JOIN eir.driverPerson drv
            LEFT JOIN EirDocumentCargoDetail edcd
              ON edcd.id = (
                SELECT MAX(sub.id) FROM EirDocumentCargoDetail sub WHERE sub.eir.id = eir.id
              )
            LEFT JOIN eir.documentCargoGof dcg
            LEFT JOIN eir.bookingGout bkg
            INNER JOIN eir.catEmptyFull catCtg
            LEFT JOIN eir.eirChassis echs
            LEFT JOIN echs.chassis chss
            LEFT JOIN echs.chassisDocumentDetail dchd
            LEFT JOIN dchd.chassisDocument dchs
            LEFT JOIN echs.chassisDocumentGo dcch
            WHERE
            eir.localSubBusinessUnit.id = :localSubBusinessUnitId
            AND cnt.id = :notApplicableContainerId
            AND eir.catMovement.id = :gateInMovementCatalogId
            AND eir.active = true
            AND eir.truckDepartureDate IS NULL 
            """)
    List<TruckDepartureListEir> truckDepartureListUnassignedGateIn(@Param("localSubBusinessUnitId") Integer localSubBusinessUnitId, @Param("gateInMovementCatalogId") Integer gateInMovementCatalogId, @Param("notApplicableContainerId") Integer notApplicableContainerId);
}
