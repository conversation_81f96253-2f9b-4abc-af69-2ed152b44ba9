package com.maersk.sd1.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class EirMultipleId implements Serializable {
    private static final long serialVersionUID = 1310585517063138996L;
    @NotNull
    @Column(name = "eir_multiple_id", nullable = false)
    private Integer eirMultipleId;

    @NotNull
    @Column(name = "eir_id", nullable = false)
    private Integer eirId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        EirMultipleId entity = (EirMultipleId) o;
        return Objects.equals(this.eirMultipleId, entity.eirMultipleId) &&
                Objects.equals(this.eirId, entity.eirId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(eirMultipleId, eirId);
    }

}