package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.dto.EquipmentConditionEIRActivityZoneDTO;
import com.maersk.sd1.common.model.EirActivityZone;
import com.maersk.sd1.sde.dto.EirActivityZoneProjection;
import com.maersk.sd1.sde.dto.EirDTO;
import com.maersk.sd1.sde.dto.NextZoneDTO;
import com.maersk.sd1.sde.dto.PTIActivityDTO;
import com.maersk.sd1.sdg.dto.EirInspectionDataDTO;
import com.maersk.sd1.sdg.dto.MtyStructureInspectionDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;


public interface EirActivityZoneRepository extends JpaRepository<EirActivityZone, Integer> {

    @Query("SELECT new com.maersk.sd1.common.dto.EquipmentConditionEIRActivityZoneDTO(e.id, e.concluded, e.isPartialInspection, " +
            "COALESCE(e.structureDamagedResult, false), COALESCE(e.machineryDamagedResult, false)) " +
            "FROM EirActivityZone e " +
            "WHERE e.eir.id = :eirId " +
            "AND e.catZoneActivity.id = :catZoneActivityId " +
            "AND e.active = true " +
            "ORDER BY e.registrationDate DESC")
    EquipmentConditionEIRActivityZoneDTO findEirActivityZoneByEirIdAndCatZoneActivity(
            @Param("eirId") Integer eirId,
            @Param("catZoneActivityId") Integer catZoneActivityId);

    @Query("SELECT ez.inspectorComment " +
            "FROM EirActivityZone ez " +
            "WHERE ez.eir.id = :eirId " +
            "AND ez.catZoneActivity.id = :catZoneActivityId " +
            "AND ez.active = true")
    String findInspectorCommentByEirIdAndCatZoneActivityIdAndActiveTrue(
            @Param("eirId") Integer eirId,
            @Param("catZoneActivityId") Integer catZoneActivityId);


    @Query("SELECT new com.maersk.sd1.sdg.dto.MtyStructureInspectionDTO(" +
            "ax.eir.id, " +
            "ax.inspectorComment, " +
            "ax.potentialFoodAid) " +
            "FROM EirActivityZone ax " +
            "WHERE ax.catZoneActivity.id = :activityInspId " +
            "AND (ax.concluded = true OR ax.isPartialInspection = true) " +
            "AND ax.active = true")
    List<MtyStructureInspectionDTO> findMtyStructureInspection(@Param("activityInspId") Integer activityInspId);


    @Query("""
                SELECT new com.maersk.sd1.sdg.dto.EirInspectionDataDTO(
                    a.eir.id,
                    a.inspectorComment,
                    COALESCE(a.potentialFoodAid, false),
                    CASE
                        WHEN a.concluded = true THEN :isInspectionFinished
                        WHEN COALESCE(a.isPartialInspection, false) = true THEN :isInspectionStarted
                        ELSE :isInspectionPending
                    END
                )
                FROM EirActivityZone a
                WHERE a.eir.id IN :eirIds
                AND a.catZoneActivity.id = :activityZoneId
                AND a.active = true
            """)
    List<EirInspectionDataDTO> findInspectionDataForEirs(
            @Param("eirIds") Set<Integer> eirIds,
            @Param("activityZoneId") Integer activityZoneId,
            @Param("isInspectionFinished") Integer isInspectionFinished,
            @Param("isInspectionStarted") Integer isInspectionStarted,
            @Param("isInspectionPending") Integer isInspectionPending
    );

    Optional<EirActivityZone> findByEirIdAndCatZoneActivityIdAndActiveTrueAndConcludedFalse(Integer eirId, Integer catZoneActivityId);

    @Query("""
            SELECT e.estimateEmr.id
            FROM EirActivityZone e
            INNER JOIN e.estimateEmr estim
            WHERE e.id = :idActividadZona
            AND estim.catEstimateType.id IN (:estimateStatusFinalized, :estimateStatusCreated)
            AND estim.active = true
            """)
    Integer findEstimadoEmrIdByActividadZonaIdAndEstimateStatus(
            @Param("idActividadZona") Integer idActividadZona,
            @Param("estimateStatusFinalized") Integer estimateStatusFinalized,
            @Param("estimateStatusCreated") Integer estimateStatusCreated
    );

    @Query(value = """
            SELECT DATEADD(HOUR, CONVERT(INT, (SELECT valor FROM SEG.unidad_negocio_config 
                                               WHERE unidad_negocio_id = B.unidad_negocio_id
                                               AND tipo_configuracion = 
                                               (SELECT catalogo_id FROM ges.catalogo WHERE alias = '20251'))),
                    B.fecha_ingreso_camion) AS FechaIngreso,
                    B.eir_id AS EIR,
                    CNTX.numero_contenedor AS Contenedor,
                    TTC.catalogo_id as TnoCnt,
                    TC.catalogo_id as TipoCnt,
                    RTRIM(B.cat_clase_cnt_id) AS ClaseCnt,
                    FORMAT(ISNULL(B.tara_cnt,0),'0') Tara,
                    FORMAT(ISNULL(B.carga_maxima_cnt,0),'0') AS CargaMaxima,
                    ISOCNT.codigo_iso_id AS iso_code_id,
                    ISOCNT.codigo_iso + ' - ' + ISOCNT.descripcion AS CodigoISO,
                    L.linea_naviera_id CodigoLinea,
                    L.linea_naviera + ' - ' + L.nombre AS DescripcionLinea,
                    sds.fn_CatalogoTraducidoDesLarga(B.cat_procedencia_id, :languageId) AS Movimiento,
                    CLI.razon_social AS Cliente,
                    rtrim(NAVEX.nombre)+'/'+RTRIM(PRONAV.viaje)  AS NVR,
                    EA.nombre AS Local,
                    B.fecha_fabricacion  AS FechaFabricacion,
                    B.cat_tipo_reefer_id  as CodigoTipoReefer,
                    ReefTipo.descripcion as TipoReefer,
                    B.cat_marca_motor_id  as CodigoMarcaMotor,
                    MAR.descripcion AS MarcaMotor,
                    IIF(:flagCargaPeligrosa=1, ( ges.fn_MensajeTraducido('PRC_GATE_IN_EMPTY',14,1)), '') AS MensajeCargaPeligrosa,
                    :zoneActivityId AS zoneActivityId,
                    ISNULL(:inspectorComment, '') AS inspector_comment,
                    A.potencial_food_aid,
                    (SELECT PNCI.imo_id
                     FROM sds.programacion_nave_contenedor_imo AS PNCI
                     INNER JOIN sds.programacion_nave_contenedor AS pnavcx (NOLOCK) 
                     ON pnavcx.programacion_nave_detalle_id = PRNADE.programacion_nave_detalle_id 
                     AND CNTX.contenedor_id = pnavcx.contenedor_id
                     WHERE PNCI.programacion_nave_contenedor_id = pnavcx.programacion_nave_contenedor_id 
                     AND PNCI.activo = 1
                     AND CNTX.contenedor_id = pnavcx.contenedor_id 
                     AND pnavcx.activo = 1
                     FOR JSON PATH) AS imo_checklist,
                    flag_cleaning_section_interior,
                    flag_cleaning_section_bottom,
                    flag_cleaning_section_right,
                    flag_cleaning_section_left,
                    flag_cleaning_section_top,
                    cat_cleaning_type_id,
                    ISNULL(:observationsComment, '') AS observations_comment,
                    IIF(B.fecha_salida_camion IS NULL, 0, 1) AS fecha_salida_camion,
                    A.es_inspeccion_parcial AS status_boolean,
                    IIF(A.es_inspeccion_parcial = 1, sds.fn_CatalogoTraducidoDes(:statusInspectionStarted, :languageId), 
                                                 sds.fn_CatalogoTraducidoDes(:statusInspectionPending, :languageId)) AS status,
                    (SELECT alias FROM ges.catalogo WHERE catalogo_id = 
                         IIF(A.es_inspeccion_parcial = 1, :statusInspectionStarted, :statusInspectionPending)) AS status_alias
            FROM [sde].[eir_actividad_zona] A (NOLOCK)
            INNER JOIN sde.eir AS B (NOLOCK) ON A.eir_id  = B.eir_id
            INNER JOIN seg.unidad_negocio AS EA ON B.sub_unidad_negocio_local_id = EA.unidad_negocio_id
            INNER JOIN sds.programacion_nave_detalle as PRNADE (NOLOCK) 
            ON B.programacion_nave_detalle_id = PRNADE.programacion_nave_detalle_id
            INNER JOIN sds.programacion_nave as PRONAV (NOLOCK) 
            ON PRNADE.programacion_nave_id = PRONAV.programacion_nave_id
            INNER JOIN sds.contenedor AS CNTX (NOLOCK) ON B.contenedor_id = CNTX.contenedor_id
            INNER JOIN ges.catalogo as TC (NOLOCK) ON B.cat_tipo_contenedor_id = TC.catalogo_id
            INNER JOIN ges.catalogo as TTC (NOLOCK) ON B.cat_tamano_cnt_id = TTC.catalogo_id
            LEFT OUTER JOIN ges.catalogo PROCED (NOLOCK) ON B.cat_procedencia_id = PROCED.catalogo_id
            INNER JOIN sds.nave AS NAVEx (NOLOCK) ON PRONAV.nave_id = NAVEx.nave_id
            LEFT OUTER JOIN sds.linea_naviera AS L (NOLOCK) ON B.linea_naviera_id = L.linea_naviera_id
            LEFT OUTER JOIN ges.empresa AS CLI (NOLOCK) ON B.empresa_cliente_id = CLI.empresa_id
            LEFT OUTER JOIN ges.catalogo AS ReefTipo (NOLOCK) ON B.cat_tipo_reefer_id = ReefTipo.catalogo_id
            LEFT OUTER JOIN ges.catalogo AS MAR (NOLOCK) ON B.cat_marca_motor_id = MAR.catalogo_id
            LEFT OUTER JOIN sds.codigo_iso as ISOCNT (NOLOCK) on B.codigo_iso_id = ISOCNT.codigo_iso_id
            WHERE A.eir_id = :eirId
            AND A.eir_actividad_zona_id = :zoneActivityId
            AND A.cat_actividad_zona_id = :isActivityInspection
            AND B.activo  = 1
            AND A.activo = 1
            AND A.concluido = 0
            """, nativeQuery = true)
    List<Object[]> findEirActivityZoneDetails(
            @Param("zoneActivityId") Integer zoneActivityId,
            @Param("flagCargaPeligrosa") Boolean flagCargaPeligrosa,
            @Param("inspectorComment") String inspectorComment,
            @Param("observationsComment") String observationsComment,
            @Param("eirId") Integer eirId,
            @Param("isActivityInspection") Integer isActivityInspection,
            @Param("statusInspectionStarted") Integer statusInspectionStarted,
            @Param("statusInspectionPending") Integer statusInspectionPending,
            @Param("languageId") Integer languageId
    );

    @Query(value = "EXEC sde.obtener_siguiente_zona :eirId, :activityType, :status", nativeQuery = true)
    List<Object[]> callObtenerSiguienteZona(
            @Param("eirId") Integer eirId,
            @Param("activityType") String activityType,
            @Param("status") Integer status
    );

    @Query("""
            SELECT new com.maersk.sd1.sde.dto.EirDTO(
                eir.subBusinessUnit.id,
                e.eir.id,
                eir.shippingLine.id,
                eir.container.id,
                cnt.containerNumber,
                eir.catContainerType.id,
                eir.catApprovalRepBox.id,
                eir.catApprovalRepMachine.id,
                e.catZoneActivity.id,
                e.startDate,
                e.endDate,
                e.id,
                e.registrationDate,
                e.structureDamagedResult,
                e.machineryDamagedResult,
                e.withSensor,
                e.withSensorDamaged,
                eir.localSubBusinessUnit.id,
                eir.catSizeCnt.id,
                eir.isoCode.id,
                eir.catTypeReefer.id,
                eir.catEngineBrand.id,
                eir.machineryWithDamage,
                eir.truckArrivalDate,
                eir.cargoMaximumCnt,
                eir.taraCnt,
                eir.catClassCnt.id,
                eir.dateManufacture,
                eir.catMovement.id,
                eir.catOrigin.id,
                e.isPartialInspection,
                null,
                null,
                null
            )
            FROM EirActivityZone e
            INNER JOIN e.eir eir
            INNER JOIN Container cnt ON eir.container.id = cnt.id
            INNER JOIN StockEmpty s ON eir.id = s.gateInEir.id
            WHERE eir.subBusinessUnit.id = :subBusinessUnitId
                AND eir.localSubBusinessUnit.id = :subBusinessUnitLocalId
                AND (:eirId IS NULL OR eir.id = :eirId)
                AND (:containerNumber IS NULL OR cnt.containerNumber LIKE CONCAT(:containerNumber, '%'))
                AND eir.active = true
                AND e.catZoneActivity.id = :boxInspectionCategoryId
                AND e.active = true
                AND e.concluded = false
                AND s.inStock = true
                AND (
                :pendingInspection IS NULL OR
                (:pendingInspection = true AND e.isPartialInspection = true) OR
                (:pendingInspection = false AND e.isPartialInspection = false))
            """)
    List<EirDTO> findEirActivitiesWhenInStock(
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("subBusinessUnitLocalId") Integer subBusinessUnitLocalId,
            @Param("eirId") Integer eirId,
            @Param("containerNumber") String containerNumber,
            @Param("boxInspectionCategoryId") Integer boxInspectionCategoryId,
            @Param("pendingInspection") Boolean pendingInspection
    );

    @Query("""
                     SELECT new com.maersk.sd1.sde.dto.EirDTO(
                         eir.subBusinessUnit.id,
                         e.eir.id,
                         eir.shippingLine.id,
                         eir.container.id,
                         cnt.containerNumber,
                         eir.catContainerType.id,
                         COALESCE(eir.catApprovalRepBox.id, 0),
                         COALESCE(eir.catApprovalRepMachine.id, 0),
                         e.catZoneActivity.id,
                         e.startDate,
                         e.endDate,
                         e.id,
                         e.registrationDate,
                         e.structureDamagedResult,
                         e.machineryDamagedResult,
                         e.withSensor,
                         e.withSensorDamaged,
                         eir.localSubBusinessUnit.id,
                         eir.catSizeCnt.id,
                         eir.isoCode.id,
                         eir.catTypeReefer.id,
                         eir.catEngineBrand.id,
                         eir.machineryWithDamage,
                         eir.truckArrivalDate,
                         eir.cargoMaximumCnt,
                         eir.taraCnt,
                         eir.catClassCnt.id,
                         eir.dateManufacture,
                         eir.catMovement.id,
                         eir.catOrigin.id,
                         e.isPartialInspection,
                         null,
                         null,
                         null
                     )
                     FROM EirActivityZone e
                     INNER JOIN e.eir eir
                     INNER JOIN Container cnt ON eir.container.id = cnt.id
                     WHERE eir.subBusinessUnit.id = :subBusinessUnitId
                         AND eir.localSubBusinessUnit.id = :subBusinessUnitLocalId
                         AND (:eirId IS NULL OR eir.id = :eirId)
                         AND (:containerNumber IS NULL OR cnt.containerNumber LIKE CONCAT(:containerNumber, '%'))
                         AND eir.active = true
                         AND e.catZoneActivity.id = :boxInspectionCategoryId
                         AND e.active = true
                         AND e.concluded = false
                         AND (
                :pendingInspection IS NULL OR
                (:pendingInspection = true AND e.isPartialInspection = true) OR
                (:pendingInspection = false AND e.isPartialInspection = false))
            """)
    List<EirDTO> findEirActivitiesWhenNotInStock(
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("subBusinessUnitLocalId") Integer subBusinessUnitLocalId,
            @Param("eirId") Integer eirId,
            @Param("containerNumber") String containerNumber,
            @Param("boxInspectionCategoryId") Integer boxInspectionCategoryId,
            @Param("pendingInspection") Boolean pendingInspection
    );

    @Query("""
                SELECT new com.maersk.sd1.sde.dto.PTIActivityDTO(
                    a.eir.id,
                    COALESCE(a.withSensor, false),
                    a.concluded,
                    a.endDate
                )
                FROM EirActivityZone a
                WHERE a.eir.id IN :eirIds
                AND a.catZoneActivity.id = :activityZoneId
                AND a.active = true
            """)
    List<PTIActivityDTO> getPTIActivities(@Param("eirIds") List<Integer> eirIds,
                                          @Param("activityZoneId") Integer activityZoneId);

    @Query(value = """
            SELECT 
                sds.fn_CatalogoTraducidoDesLarga(a.cat_actividad_zona_id, :languageId) + ' (' + actx.descripcion + ')' as actividad,
                IIF(a.concluido = 1, ges.fn_MensajeTraducido('SI', 1, :languageId), IIF(a.activo = 0, '--', 'No')) as concluido,
                CASE a.cat_actividad_zona_id 
                    WHEN 43162 THEN 
                        IIF(a.concluido = 1, 
                            IIF(ISNULL(a.resultado_estructura_danada, 0) = 1, ges.fn_MensajeTraducido('SI', 1, :languageId), 'No'), 
                            ges.fn_MensajeTraducido('EIR_LISTADO', 4, :languageId)) 
                    ELSE '--' 
                END as estructuraDanada,
                CASE a.cat_actividad_zona_id 
                    WHEN 43163 THEN 
                        IIF(a.concluido = 1, 
                            IIF(ISNULL(a.resultado_maquinaria_danada, 0) = 1, ges.fn_MensajeTraducido('SI', 1, :languageId), 'No'), 
                            ges.fn_MensajeTraducido('EIR_LISTADO', 4, :languageId)) 
                    ELSE '--' 
                END as maquinariaDanada,
                sds.fn_CatalogoTraducidoDesLarga(a.cat_actividad_zona_resultado_id, :languageId) + ' (' + actxRe.descripcion + ')' as zonaResultado,
                FORMAT(seg.fn_datetime_get(:subBusinessUnitId, a.fecha_inicio), :formatDatetime) as fInicio,
                FORMAT(seg.fn_datetime_get(:subBusinessUnitId, a.fecha_termino), :formatDatetime) as fTermino,
                CASE a.cat_actividad_zona_id 
                    WHEN 43163 THEN 
                        IIF(a.concluido = 1, 
                            IIF(ISNULL(a.con_sensor, 0) = 1, 
                                IIF(ISNULL(con_sensor_danado, 0) = 1, ges.fn_MensajeTraducido('EIR_LISTADO', 2, :languageId), ges.fn_MensajeTraducido('EIR_LISTADO', 1, :languageId)), 
                                ges.fn_MensajeTraducido('EIR_LISTADO', 3, :languageId)), 
                            ges.fn_MensajeTraducido('EIR_LISTADO', 5, :languageId)) 
                    ELSE '--' 
                END as sensor,
                CASE a.cat_actividad_zona_id 
                    WHEN 43163 THEN 
                        IIF(a.concluido = 1, 
                            IIF(ISNULL(a.rcd, 0) = 1, ges.fn_MensajeTraducido('SI', 1, :languageId), 'No'), 
                            ges.fn_MensajeTraducido('EIR_LISTADO', 5, :languageId)) 
                    ELSE '--' 
                END as rcd,
                CASE 
                    WHEN a.cat_actividad_zona_id IN (43163, 43162) THEN 
                        IIF(a.concluido = 1, 
                            IIF(ISNULL(a.inspeccion_compleja, 0) = 1, ges.fn_MensajeTraducido('SI', 1, :languageId), 'No'), 
                            ges.fn_MensajeTraducido('EIR_LISTADO', 6, :languageId)) 
                    ELSE '--' 
                END as inspeccionCompleja,
                sds.fn_CatalogoTraducidoDes(a.cat_complejidad_dano_id, :languageId) as complejidadDano,
                IIF(ISNULL(a.es_inspeccion_parcial, 0) = 1, ges.fn_MensajeTraducido('SI', 1, :languageId) + ' (' + FORMAT(a.fecha_insp_parcial, 'dd/MM/yy HH:mm') + ')', '') as inspeccionParcial,
                IIF(ISNULL(a.requiere_lavado_especial, 0) = 1, ges.fn_MensajeTraducido('SI', 1, :languageId), '') as lavadoEspecial,
                IIF(ISNULL(a.bloquear_contenedor, 0) = 1, ges.fn_MensajeTraducido('SI', 1, :languageId), '') as bloquearContenedor,
                ISNULL(a.precinto_bloqueo, '') + IIF(ISNULL(a.precinto_bloqueo, '') = '', '', ' / ' + a.precinto_preasignado) as precinto,
                CONCAT(URE.nombres, ' ', URE.apellido_paterno, ' ', URE.apellido_materno) as usuarioInicio,
                CONCAT(URT.nombres, ' ', URT.apellido_paterno, ' ', URT.apellido_materno) as usuarioTermino,
                FORMAT(seg.fn_datetime_get(:subBusinessUnitId, a.fecha_registro), :formatDatetime) as fCreacion
            FROM sde.eir_actividad_zona as a (NOLOCK)
            INNER JOIN ges.catalogo as actx (NOLOCK) ON a.cat_actividad_zona_id = actx.catalogo_id
            LEFT OUTER JOIN ges.catalogo as actxRe (NOLOCK) ON a.cat_actividad_zona_resultado_id = actxRe.catalogo_id
            INNER JOIN seg.usuario URE (NOLOCK) ON a.usuario_inicio_id = URE.usuario_id
            LEFT OUTER JOIN seg.usuario URT (NOLOCK) ON a.usuario_termino_id = URT.usuario_id
            WHERE a.eir_id = :eirId
            ORDER BY a.fecha_inicio DESC, a.fecha_registro DESC
            """, nativeQuery = true)
    List<EirActivityZoneProjection> findEirActivityZoneByEirId(
            @Param("eirId") Integer eirId,
            @Param("languageId") Integer languageId,
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("formatDatetime") String formatDatetime
    );

    @Query("""
            SELECT eaz FROM EirActivityZone eaz
            JOIN eaz.eir e
            WHERE e.id = :eirId
              AND eaz.catZoneActivity.id = :isActivityPTI
              AND eaz.active = true
              AND e.active = true
              AND eaz.concluded = false
            """)
    EirActivityZone findPtiActivityZoneByEirId(@Param("eirId") Integer eirId, @Param("isActivityPTI") Integer isActivityPTI);

    @Query("""
                SELECT COUNT(eaz) > 0 FROM EirActivityZone eaz
                WHERE eaz.id = :eirActivityZoneId
                AND eaz.concluded = false
            """)
    boolean isConcludedZoneExist(@Param("eirActivityZoneId") Integer eirActivityZoneId);

    @Query("""
            SELECT eaz.estimateEmr.id
            FROM EirActivityZone eaz
            JOIN eaz.estimateEmr ee
            WHERE eaz.id = :activityZoneId
              AND ee.catEstimateStatus.id IN :estimateStatuses
              AND ee.active = true
            """)
    Optional<Integer> findEmrNumber(
            @Param("activityZoneId") Integer activityZoneId,
            @Param("estimateStatuses") List<Integer> estimateStatuses
    );

    @Procedure(name = "GetNextZone")
    List<NextZoneDTO> getNextZone(@Param("eir_id") Integer eirId,
                                  @Param("Actividad") String activity,
                                  @Param("ConDano") Boolean hasDamage);


    boolean existsByEirIdAndCatZoneActivity_DescriptionAndConcluded(Integer eirId, String zoneCode, boolean concluded);

    @Query("SELECT MAX(e.endDate) " +
            "FROM EirActivityZone e " +
            "WHERE e.eir.id = :eirId " +
            "AND e.catZoneActivity.description IN ('PTI', 'LAV', 'INSP') " +
            "AND e.catZoneActivity.description = :zoneCode")
    LocalDateTime findLastEndDateForActivity(@Param("eirId") Integer eirId, @Param("zoneCode") String zoneCode);


    boolean existsByEirIdAndCatZoneActivity_DescriptionAndBlockContainer(Integer eirId, String zoneCode, boolean blockContainer);

    @Query("SELECT CASE WHEN COUNT(e) > 0 THEN TRUE ELSE FALSE END " +
            "FROM EirActivityZone e " +
            "WHERE e.eir.id = :eirId " +
            "AND e.catZoneActivity.description = 'PTI' " +
            "AND e.withSensor = TRUE")
    Boolean findSensorInPTI(@Param("eirId") Integer eirId);


    @Query("SELECT ea FROM EirActivityZone ea "
            + "JOIN ea.eir e "
            + "JOIN StockEmpty sv ON sv.gateInEir = e AND sv.inStock = true AND sv.active = true "
            + "JOIN e.container cnt "
            + "WHERE e.subBusinessUnit.id = :subUnidadNegocioId "
            + "AND e.localSubBusinessUnit.id = :subUnidadNegocioLocalId "
            + "AND ea.catZoneActivity.id = 43163 "
            + "AND ea.active = true "
            + "AND ea.concluded = false "
            + "AND e.active = true "
            + "AND (:eirId IS NULL OR e.id = :eirId) "
            + "AND (:contenedor IS NULL OR cnt.containerNumber LIKE CONCAT(:contenedor, '%')) "
            + "AND (:pendingInspectionVal IS NULL OR "
            + "     (:pendingInspectionVal = 0 AND ea.isPartialInspection = false) OR "
            + "     (:pendingInspectionVal = 1 AND ea.isPartialInspection = true))")
    List<EirActivityZone> findEirActivityZoneEnStock(@Param("subUnidadNegocioId") Long subUnidadNegocioId,
                                                     @Param("subUnidadNegocioLocalId") Long subUnidadNegocioLocalId,
                                                     @Param("eirId") Integer eirId,
                                                     @Param("contenedor") String contenedor,
                                                     @Param("pendingInspectionVal") Integer pendingInspectionVal);

    @Query("SELECT ea FROM EirActivityZone ea "
            + "JOIN ea.eir e "
            + "JOIN e.container cnt "
            + "WHERE e.subBusinessUnit.id = :subUnidadNegocioId "
            + "AND e.localSubBusinessUnit.id = :subUnidadNegocioLocalId "
            + "AND ea.catZoneActivity.id = 43163 "
            + "AND ea.active = true "
            + "AND ea.concluded = false "
            + "AND e.active = true "
            + "AND (:eirId IS NULL OR e.id = :eirId) "
            + "AND (:contenedor IS NULL OR cnt.containerNumber LIKE CONCAT(:contenedor, '%')) "
            + "AND (:pendingInspectionVal IS NULL OR "
            + "     (:pendingInspectionVal = 0 AND ea.isPartialInspection = false) OR "
            + "     (:pendingInspectionVal = 1 AND ea.isPartialInspection = true))")
    List<EirActivityZone> findEirActivityZoneNotEnStock(@Param("subUnidadNegocioId") Long subUnidadNegocioId,
                                                        @Param("subUnidadNegocioLocalId") Long subUnidadNegocioLocalId,
                                                        @Param("eirId") Integer eirId,
                                                        @Param("contenedor") String contenedor,
                                                        @Param("pendingInspectionVal") Integer pendingInspectionVal);

    @Query("SELECT ea FROM EirActivityZone ea "
            + "JOIN FETCH ea.eir e "
            + "WHERE ea.active = :isActive AND ea.catZoneActivity.id = :zoneActivityId "
            + "AND e.id IN :eirIds")
    List<EirActivityZone> findInspectionActivity(@Param("isActive") Boolean isActive, @Param("zoneActivityId") Integer zoneActivityId, @Param("eirIds") List<Integer> eirIds);

    @Query("SELECT eaz FROM EirActivityZone eaz WHERE eaz.eir.id = :eirId "
            + "AND eaz.catZoneActivity.id = :catZoneActivityId "
            + "AND eaz.concluded = true AND eaz.active = true")
    Optional<EirActivityZone> findActivityZone(@Param("eirId") Integer eirId,
                                               @Param("catZoneActivityId") Integer catZoneActivityId);
    @Query("""
                SELECT eaz
                FROM EirActivityZone eaz
                WHERE eaz.eir.id = :eirId
                  AND eaz.active = true
            """)
    List<EirActivityZone> findByEirIdAndActiveTrue(@Param("eirId") Integer eirId);

    @Query("SELECT e " +
            "FROM EirActivityZone e " +
            "WHERE e.eir.id = :eirId " +
            "AND e.catZoneActivity.id = :catZoneActivityId " +
            "AND e.active = true ")
    Optional<EirActivityZone> findByEirIdAndCatZoneActivity(
            @Param("eirId") Integer eirId,
            @Param("catZoneActivityId") Integer catZoneActivityId);

            @Query("SELECT ez " +
            "FROM EirActivityZone ez " +
            "WHERE ez.eir.id = :eirId " +
            "AND ez.catZoneActivity.id = :catZoneActivityId " +
            "AND ez.active = true " +
            "AND ez.eir.active = true ")
    Optional<EirActivityZone> findByEirIdAndCatZoneActivityIdAndActiveTrue(
            @Param("eirId") Integer eirId,
            @Param("catZoneActivityId") Integer catZoneActivityId);
}

