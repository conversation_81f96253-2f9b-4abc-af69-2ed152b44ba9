package com.maersk.sd1.common.repository;
import com.maersk.sd1.adm.dto.PersonByBusinessUnitListOutput;

import com.maersk.sd1.common.model.UserBU;
import com.maersk.sd1.common.model.UserOneId;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface UserBURepository extends JpaRepository<UserBU, UserOneId> {

    @Modifying
    @Query("DELETE FROM UserBU ubu WHERE ubu.user.id = :userId")
    void deleteByUserId(@Param("userId") Integer userId);
    List<UserBU> findByUserId( Integer usuarioId);

    @Query(value = "SELECT COUNT(u.id.userId) FROM UserBU u WHERE u.id.userId = :userId")
    long countByUserId(@Param("userId") Long userId);

    @Query("SELECT new com.maersk.sd1.adm.dto.PersonByBusinessUnitListOutput$PersonItemDto(" +
            " p.id, CONCAT(p.names, ' ', p.firstLastName, ' ', COALESCE(p.secondLastName, '')) ) " +
            "FROM UserBU ubu " +
            "JOIN ubu.user u " +
            "JOIN u.person p " +
            "JOIN PersonRole pr ON p.id = pr.person.id " +
            "JOIN pr.catPersonRole c " +
            "WHERE ubu.businessUnit.id = :businessUnitId " +
            "AND (:personName IS NULL OR LOWER(CONCAT(p.names, ' ', p.firstLastName, ' ', COALESCE(p.secondLastName, ''))) LIKE CONCAT('%',LOWER(:personName),'%')) " +
            "AND c.alias IN :roles")
    List<PersonByBusinessUnitListOutput.PersonItemDto> findPersonList(
            @Param("businessUnitId") Integer businessUnitId,
            @Param("personName") String personName,
            @Param("roles") List<String> roles,
            Pageable pageable
    );
}