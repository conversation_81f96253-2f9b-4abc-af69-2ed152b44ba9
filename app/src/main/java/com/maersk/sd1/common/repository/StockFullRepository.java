package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.StockFull;
import com.maersk.sd1.sdg.dto.TbDataEmpty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;


public interface StockFullRepository extends JpaRepository<StockFull, Integer> {

    @Query("select s from StockFull s where s.container.id = :containerId and s.subBusinessUnit.id = :subBusinessUnitId")
    StockFull findByContainerAndSubBusinessUnit(@Param("containerId") Integer containerId, @Param("subBusinessUnitId") Integer subBusinessUnitId);

    @Query( "SELECT stk.id " +
            "FROM StockFull stk " +
            "JOIN BusinessUnit bb ON bb.id = stk.subBusinessUnit.id " +
            "WHERE stk.container.id = :containerId " +
            "AND bb.parentBusinessUnit.id = :businessUnitId " +
            "AND stk.inStock = true " +
            "AND stk.active = true")
    Integer validateStockGateInContainer(@Param("containerId") Integer containerId,
                                   @Param("businessUnitId") Integer businessUnitId);

    @Modifying
    @Transactional
    @Query("UPDATE StockFull stk " +
            "SET stk.inStock = false, " +
            "stk.modificationUser = :userId, " +
            "stk.modificationDate = CURRENT_TIMESTAMP, " +
            "stk.traceStock = :messageTrace " +
            "WHERE stk.id = :stockEmptyId")
    void updateStockFullInStockFalse(@Param("userId") Integer userId,
                                      @Param("stockEmptyId") Integer stockEmptyId,
                                      @Param("messageTrace") String messageTrace);

    @Query( "SELECT stk.id " +
            "FROM StockFull stk " +
            "WHERE stk.container.id = :containerId " +
            "AND stk.subBusinessUnit.id = :subBusinessUnitId " +
            "AND stk.inStock = true " +
            "AND stk.active = true")
    Integer validateStockGateOutContainer(@Param("containerId") Integer containerId,
                                          @Param("subBusinessUnitId") Integer subBusinessUnitId);

    @Query("SELECT s.gateInEir.id FROM StockFull s " +
            "WHERE s.gateOutEir.id = :eirId " +
            "AND s.container.id = :containerId")
    Integer findEirInIdByEirOutIdAndContainerId(@Param("eirId") Integer eirId,
                                                @Param("containerId") Integer containerId);

    @Query("SELECT s.gateInEir.id FROM StockFull s " +
            "WHERE s.gateOutEir.id = :eirId " +
            "AND s.active = true")
    Integer findEirInIdByEirOutId(@Param("eirId") Integer eirId);


    @Query(
            value = "SELECT " +
                    "a.unidad_negocio_id, a.sub_unidad_negocio_id, a.programacion_nave_detalle_id, a.contenedor_id, null AS chassis_id, " +
                    "a.cat_empty_full_id, Tlocal.nombre AS Local, A.eir_id, A.fecha_ingreso_camion, A.fecha_salida_camion, cntx.numero_contenedor AS equipment_number, " +
                    "cntx.cat_tamano_id, cntx.cat_tipo_contenedor_id, :is_container, cntx.cat_clase_id, cntx.codigo_iso_id, cntx.linea_naviera_id AS shipping_line, " +
                    "NULL AS chassis_owner_company_id, a.fecha_registro, " +
                    "COALESCE(precinto_1,'') + " +
                    "IIF(COALESCE(precinto_2,'') <> '', ', ', '') + COALESCE(precinto_2,'') + " +
                    "IIF(COALESCE(precinto_3,'') <> '', ', ', '') + COALESCE(precinto_3,'') + " +
                    "IIF(COALESCE(precinto_4,'') <> '', ', ', '') + COALESCE(precinto_4,'') AS seals, " +
                    "a.observacion, NULL AS cat_cargo_document_type_id, NULL AS cargo_document, NULL AS shipper_nro, NULL AS shipper_name, " +
                    "NULL AS consignee_nro, NULL AS consignee_name, NULL AS operation_type, NULL AS producto_id, A.eir_chassis_id, " +
                    "sdg.fn_GetEquipmentConditionID(a.eir_id, :is_container, 'S', 'CUR') AS cat_structure_condition_id, " +
                    "sdg.fn_GetEquipmentConditionID(a.eir_id, :is_container, 'M', 'CUR') AS cat_machinery_condition_id, a.flag_chassis_stayed, " +
                    "0 AS filter, NULL AS inspector_comment, 0 AS potencial_food_aid, a.observacion AS gin_comment, 0 AS USDA_approved, " +
                    "sdg.fn_GetEquipmentConditionID(a.eir_id, :is_container, 'S', 'INS') AS cat_structure_condition_insp_id, " +
                    "sdg.fn_GetEquipmentConditionID(a.eir_id, :is_container, 'M', 'INS') AS cat_machinery_condition_insp_id, NULL AS booking_pre_allocation " +
                    "FROM sdf.stock_full AS stockf " +
                    "INNER JOIN sde.eir AS A ON stockf.eir_gatein_id = A.eir_id " +
                    "INNER JOIN sds.contenedor AS cntx ON A.contenedor_id = cntx.contenedor_id " +
                    "INNER JOIN seg.unidad_negocio AS Tlocal ON a.sub_unidad_negocio_local_id = Tlocal.unidad_negocio_id " +
                    "WHERE A.sub_unidad_negocio_id = :sub_business_unit_id " +
                    "AND A.cat_empty_full_id = COALESCE(:empty_full_id, A.cat_empty_full_id) " +
                    "AND cntx.cat_tamano_id = COALESCE(:container_size_id, cntx.cat_tamano_id) " +
                    "AND cntx.cat_tipo_contenedor_id = COALESCE(:container_type_id, cntx.cat_tipo_contenedor_id) " +
                    "AND COALESCE(cntx.cat_clase_id, 0) = COALESCE(:container_grade_id, COALESCE(cntx.cat_clase_id, 0)) " +
                    "AND A.linea_naviera_id = COALESCE(:container_shipping_line_id, A.linea_naviera_id) " +
                    "AND A.contenedor_id NOT IN (:equipment_pending_id, :equipment_not_applicable_id) " +
                    "AND A.fecha_salida_camion IS NOT NULL " +
                    "AND stockf.in_stock = 1",
            nativeQuery = true)
    List<TbDataEmpty> updateEquipmentDetails(
            @Param("is_container") Integer isContainer,
            @Param("sub_business_unit_id") Integer subBusinessUnitId,
            @Param("empty_full_id") Integer emptyFullId,
            @Param("container_size_id") Integer containerSizeId,
            @Param("container_type_id") Integer containerTypeId,
            @Param("container_grade_id") Integer containerGradeId,
            @Param("container_shipping_line_id") Integer containerShippingLineId,
            @Param("equipment_pending_id") Integer equipmentPendingId,
            @Param("equipment_not_applicable_id") Integer equipmentNotApplicableId
    );


    @Query(value = """
            SELECT
                NULL AS id,
                a.unidad_negocio_id AS businessUnitId,
                a.sub_unidad_negocio_id AS subBusinessUnitId,
                a.programacion_nave_detalle_id AS programmingShipDetailId,
                a.contenedor_id AS containerId,
                NULL AS chassisId,
                a.cat_empty_full_id AS catEmptyFullId,
                tlocal.nombre AS local,
                a.eir_id AS eirId,
                a.fecha_ingreso_camion AS truckEntryDate,
                a.fecha_salida_camion AS truckExitDate,
                cntx.numero_contenedor AS equipmentNumber,
                cntx.cat_tamano_id AS equipmentSizeId,
                cntx.cat_tipo_contenedor_id AS equipmentTypeId,
                :isContainer AS equipmentCategory,
                cntx.cat_clase_id AS equipmentClassId,
                cntx.codigo_iso_id AS isoCode,
                cntx.linea_naviera_id AS shippingLineId,
                NULL AS chassisOwnerCompanyId,
                a.fecha_registro AS registrationDate,
                ISNULL(precinto_1, '') +
                IIF(ISNULL(precinto_2, '') <> '', ', ', '') + ISNULL(precinto_2, '') +
                IIF(ISNULL(precinto_3, '') <> '', ', ', '') + ISNULL(precinto_3, '') +
                IIF(ISNULL(precinto_4, '') <> '', ', ', '') + ISNULL(precinto_4, '') AS seals,
                a.observacion AS observation,
                NULL AS cargoDocumentTypeId,
                NULL AS cargoDocument,
                NULL AS shipperNumber,
                NULL AS shipperName,
                NULL AS consigneeNumber,
                NULL AS consigneeName,
                NULL AS operationType,
                NULL AS productId,
                a.eir_chassis_id AS eirChassisId,
                sdg.fn_GetEquipmentConditionID(a.eir_id, :isContainer, 'S', 'CUR') AS structureConditionId,
                sdg.fn_GetEquipmentConditionID(a.eir_id, :isContainer, 'M', 'CUR') AS machineryConditionId,
                a.flag_chassis_stayed AS chassisStayedFlag,
                0 AS filter,
                NULL AS inspectorComment,
                0 AS potentialFoodAid,
                a.observacion AS ginComment,
                0 AS usdaApproved,
                sdg.fn_GetEquipmentConditionID(a.eir_id, :isContainer, 'S', 'INS') AS structureConditionInspectionId,
                sdg.fn_GetEquipmentConditionID(a.eir_id, :isContainer, 'M', 'INS') AS machineryConditionInspectionId,
                NULL AS bookingPreAllocation,
                NULL AS inspectionStatus,
                NULL AS estimateStructureNumber,
                NULL AS estimateStructureStatus,
                NULL AS estimateMachineNumber,
                NULL AS estimateMachineStatus,
                a.flag_no_maersk AS nonMaerskFlag,
                cntx.fecha_fabricacion AS manufactureDateContainer
            FROM sdf.stock_full AS stockf
            INNER JOIN sde.eir AS a ON stockf.eir_gatein_id = a.eir_id
            INNER JOIN sds.contenedor AS cntx ON a.contenedor_id = cntx.contenedor_id
            INNER JOIN seg.unidad_negocio AS tlocal ON a.sub_unidad_negocio_local_id = tlocal.unidad_negocio_id
            WHERE a.sub_unidad_negocio_id = :subBusinessUnitId
            AND a.cat_empty_full_id = ISNULL(:emptyFullId, a.cat_empty_full_id)
            AND cntx.cat_tamano_id = ISNULL(:containerSizeId, cntx.cat_tamano_id)
            AND cntx.cat_tipo_contenedor_id = ISNULL(:containerTypeId, cntx.cat_tipo_contenedor_id)
            AND ISNULL(cntx.cat_clase_id, 0) = ISNULL(:containerGradeId, ISNULL(cntx.cat_clase_id, 0))
            AND a.linea_naviera_id = ISNULL(:shippingLineId, a.linea_naviera_id)
            AND COALESCE(a.flag_no_maersk, 0) = COALESCE(:nonMaerskFlag, COALESCE(a.flag_no_maersk, 0))
            AND cntx.numero_contenedor LIKE CONCAT('%', TRIM(:equipmentNumber), '%')
            AND NOT a.contenedor_id IN (:equipmentPendingId, :equipmentNotApplicableId)
            AND a.fecha_salida_camion IS NOT NULL
            AND stockf.in_stock = 1
            """, nativeQuery = true)
    List<Object[]> fetchFullContainerStockData(
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("emptyFullId") Integer emptyFullId,
            @Param("containerSizeId") Integer containerSizeId,
            @Param("containerTypeId") Integer containerTypeId,
            @Param("containerGradeId") Integer containerGradeId,
            @Param("shippingLineId") Integer shippingLineId,
            @Param("nonMaerskFlag") String nonMaerskFlag,
            @Param("equipmentNumber") String equipmentNumber,
            @Param("equipmentPendingId") Integer equipmentPendingId,
            @Param("equipmentNotApplicableId") Integer equipmentNotApplicableId,
            @Param("isContainer") Integer isContainer
    );

    @Query("SELECT sf FROM StockFull sf WHERE sf.gateOutEir.id = :gateOutEirId AND sf.active = true")
    Optional<StockFull> findActiveByGateOutEir(Integer gateOutEirId);

    @Query("SELECT CASE WHEN count(sf) > 0 THEN true ELSE false END FROM StockFull sf WHERE sf.gateInEir.id = :gateInEirId AND sf.gateOutEir IS NULL")
    boolean existsByGateInEirWithNoGateOutEir(Integer gateInEirId);

    @Query("SELECT sf FROM StockFull sf WHERE sf.gateInEir.id = :gateInEirId AND sf.active = true")
    Optional<StockFull> findActiveByGateInEir(Integer gateInEirId);
    
    @Query("SELECT sf FROM StockFull sf " +
            "JOIN sf.container c " +
            "JOIN sf.gateInEir eir " +
            "WHERE sf.subBusinessUnit.id = :subBusinessUnitId " +
            "  AND sf.inStock = true " +
            "  AND (:containerNumber IS NULL OR c.containerNumber LIKE CONCAT('%', :containerNumber, '%'))")
    List<StockFull> findInStockFull(@Param("subBusinessUnitId") Integer subBusinessUnitId,
                                    @Param("containerNumber") String containerNumber);

    @Query("SELECT COUNT(sf) FROM StockFull sf " +
            "JOIN sf.container c " +
            "JOIN sf.gateInEir eir " +
            "WHERE sf.subBusinessUnit.id = :subBusinessUnitId " +
            "  AND sf.inStock = true " +
            "  AND (:containerNumber IS NULL OR c.containerNumber LIKE CONCAT('%', :containerNumber, '%'))")
    long countInStockFull(@Param("subBusinessUnitId") Integer subBusinessUnitId,
                          @Param("containerNumber") String containerNumber);

    @Query("SELECT COUNT(s) FROM StockFull s WHERE s.gateInEir.id = :eirId AND s.inStock = true")
    long countStockByEirGateIn(@Param("eirId") Integer eirId);

    @Query("SELECT sf FROM StockFull sf WHERE sf.gateInEir.id = :eirGateInId AND sf.active = true")
    StockFull findStockFullByGateIn(@Param("eirGateInId") Integer eirGateInId);
    @Procedure(name = "StockFull.gateOutFullCheckStockScheduled")
    List<Object> gateOutFullCheckStockScheduled(@Param("sub_business_unit_id") Integer subBusinessUnitId, @Param("language_id") Integer languageId);

    @Query("""
            SELECT sf 
            FROM StockFull sf
            WHERE sf.container.containerNumber IN :containerNumbers
            AND sf.gateOutEir.subBusinessUnit.id = :subBusinessUnitId
            AND sf.active
            """)
    List<StockFull> findAllInStockByContainerNumberAndBusinessUnit(List<String> containerNumbers, Integer subBusinessUnitId);
    @Query("SELECT sf FROM StockFull sf "
            + " WHERE sf.active = true "
            + "   AND sf.inStock = false "
            + "   AND sf.gateOutEir IS NULL "
            + "   AND sf.subBusinessUnit.id = :subBusinessUnitId "
            + "   AND sf.container.containerNumber IN :containerNumbers")
    List<StockFull> findNotInStockContainers(@Param("subBusinessUnitId") Integer subBusinessUnitId,
                                             @Param("containerNumbers") List<String> containerNumbers);

    @Query("""
            SELECT TRUE FROM StockFull sf
            WHERE sf.container.id = :containerId
            AND sf.subBusinessUnit.id = :subBusinessUnitId
            AND sf.inStock = :inStock
            AND sf.active = :active
            """)
    Boolean existsByContainerIdAndSubBusinessUnitIdAndInStockAndActive(Integer containerId, Integer subBusinessUnitId, boolean inStock, boolean active);

    @Query("""
            SELECT TRUE FROM StockFull sf
            WHERE sf.gateInEir.id = :eirId
            AND sf.inStock = TRUE
            AND sf.active = TRUE
            """)
    boolean isInStockWithEir(Integer eirId);
}

