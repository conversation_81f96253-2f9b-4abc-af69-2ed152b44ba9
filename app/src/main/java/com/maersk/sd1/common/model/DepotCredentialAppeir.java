package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "depot_credential_appeir", schema = "sde")
public class DepotCredentialAppeir {

    @Id
    @Column(name = "depot_credential_appeir_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "sub_business_unit_id", nullable = false)
    private BusinessUnit subBusinessUnit;

    @Size(max = 500)
    @Column(name = "url", length = 500)
    private String url;

    @Size(max = 100)
    @Column(name = "client_id", length = 100)
    private String clientId;

    @Size(max = 100)
    @Column(name = "client_secret", length = 100)
    private String clientSecret;

    @Size(max = 200)
    @Column(name = "shop_email_copy", length = 200)
    private String shopEmail;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @NotNull
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

    @NotNull
    @Column(name = "shipping_line_id", nullable = false)
    private Integer shippingLineId;

    @NotNull
    @Column(name = "business_unit_id", nullable = false)
    private Integer businessUnitId;

    @NotNull
    @Column(name = "send_gate_in", nullable = false)
    private Boolean sendGateIn = false;

    @NotNull
    @Column(name = "send_gate_out", nullable = false)
    private Boolean sendGateOut = false;

}