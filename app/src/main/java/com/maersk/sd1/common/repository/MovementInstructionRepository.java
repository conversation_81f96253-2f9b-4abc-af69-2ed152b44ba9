package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.MovementInstruction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface MovementInstructionRepository extends JpaRepository<MovementInstruction, Integer> {
    @Modifying
    @Transactional
    @Query("UPDATE MovementInstruction m " +
            "SET m.comment = 'RETURNED', m.modificationUser.id = :userId " +
            "WHERE m.id = :movementInstructionId")
    int markMovementInstructionReturned(@Param("userId") Integer userId,
                                        @Param("movementInstructionId") Integer movementInstructionId);

    @Modifying
    @Query("""
            UPDATE MovementInstruction m
            SET m.catStatus.id = :cancelledMovementInstructionStatus,
                m.modificationUser.id = :userId,
                m.modificationDate = CURRENT_TIMESTAMP,
                m.comment = :trace
            WHERE m.eir.id = :eirId
            AND m.catStatus.id NOT IN :notPendingMovementInstructionStatus
            """)
    void disablePendingMovementInstruction(Integer eirId, Integer cancelledMovementInstructionStatus, List<Integer> notPendingMovementInstructionStatus, Integer userId, String trace);
}