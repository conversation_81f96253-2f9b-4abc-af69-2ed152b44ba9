package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.PersonRole;
import com.maersk.sd1.common.model.PersonRoleId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface PersonRoleRepository extends JpaRepository<PersonRole, PersonRoleId> {
    @Query("SELECT pr FROM PersonRole pr " +
            "WHERE pr.person.identificationDocument = :docId " +
            "  AND pr.catPersonRole.id = 41567 " +
            "  AND pr.person.active = true")
    List<PersonRole> findDriverByDoc(@Param("docId") String docId);

    @Modifying
    @Transactional
    @Query("DELETE FROM PersonRole pr WHERE pr.person.id = :personId")
    void deleteByPersonId(@Param("personId") Integer personId);


    @Query("SELECT pr FROM PersonRole pr WHERE pr.person.id = :personId")
    List<PersonRole> findByPersonId(@Param("personId") Integer personId);

    List<PersonRole> findByIdPersonId(Integer personId);

}