package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Catalog;
import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "notificacion_correo_marca", schema = "seg")
public class NotificationMailBrand {
    @EmbeddedId
    private NotificationMailBrandId id;

    @MapsId("typeReferenceId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "tipo_referencia_id", nullable = false)
    private Catalog catReferenceType;

}