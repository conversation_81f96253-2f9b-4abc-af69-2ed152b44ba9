package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.common.model.StockEmpty;
import com.maersk.sd1.sdg.dto.StockEmptyDetailsDTO;
import com.maersk.sd1.sdg.dto.TbDataEmpty;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Primary
@Repository
public interface StockEmptyRepository extends JpaRepository<StockEmpty, Integer> {

    @Query("SELECT stk.id " +
            "FROM StockEmpty stk " +
            "JOIN BusinessUnit bb ON bb.id = stk.subBusinessUnit.id " +
            "WHERE stk.container.id = :containerId " +
            "AND bb.parentBusinessUnit.id = :businessUnitId " +
            "AND stk.inStock = true " +
            "AND stk.active = true")
    Integer validateStockGateInContainer(@Param("containerId") Integer containerId,
                                         @Param("businessUnitId") Integer businessUnitId);

    @Modifying
    @Transactional
    @Query("UPDATE StockEmpty stk " +
            "SET stk.inStock = false, " +
            "stk.modificationUser.id = :userId, " +
            "stk.modificationDate = CURRENT_TIMESTAMP, " +
            "stk.traceStock = :messageTrace " +
            "WHERE stk.id = :stockEmptyId")
    void updateStockEmptyInStockFalse(@Param("userId") Integer userId,
                                      @Param("stockEmptyId") Integer stockEmptyId,
                                      @Param("messageTrace") String messageTrace);

    @Query("SELECT stk.id " +
            "FROM StockEmpty stk " +
            "WHERE stk.container.id = :containerId " +
            "AND stk.subBusinessUnit.id = :subBusinessUnitId " +
            "AND stk.inStock = true " +
            "AND stk.active = true")
    Integer validateStockGateOutContainer(@Param("containerId") Integer containerId,
                                          @Param("subBusinessUnitId") Integer subBusinessUnitId);

    @Query("SELECT s.gateOutEir FROM StockEmpty s " +
            "WHERE s.gateInEir.id = :eirId " +
            "AND s.container.id = :containerId")
    Integer findEirOutIdByEirInIdAndContainerId(@Param("eirId") Integer eirId,
                                                @Param("containerId") Integer containerId);

    @Query("select s from StockEmpty s where s.container.id = :containerId and s.subBusinessUnit.id = :subBusinessUnitId")
    StockEmpty findByContainerAndSubBusinessUnit(@Param("containerId") Integer containerId, @Param("subBusinessUnitId") Integer subBusinessUnitId);

    @Query("SELECT s.gateInEir.id FROM StockEmpty s " +
            "WHERE s.gateOutEir.id = :eirId AND s.active = true ")
    Integer findEirGateInIdByEirGateOutId(@Param("eirId") Integer eirId);

    @Query( "SELECT stk.gateInEir.id " +
            "FROM StockEmpty stk " +
            "WHERE stk.gateOutEir.id = :eirOutId " +
            "AND stk.active = true")
    Integer findEIRInByEIROut(@Param("eirOutId") Integer eirOutId);

    @Query("SELECT new com.maersk.sd1.sdg.dto.StockEmptyDetailsDTO(stv.gateInEir.id, catalog.code) " +
            "FROM StockEmpty stv " +
            "JOIN stv.gateOutEir eir " +
            "JOIN eir.catOrigin catalog " +
            "WHERE eir.id = :eirId")
    List<StockEmptyDetailsDTO> findEirGateinDetails(@Param("eirId") Integer eirId);



    @Query(value = "SELECT a.unidad_negocio_id AS businessUnitId, " +
            "a.sub_unidad_negocio_id AS subBusinessUnitId, " +
            "a.programacion_nave_detalle_id AS programacionNaveDetalleId, " +
            "a.contenedor_id AS contenedorId, " +
            "null AS chassisId, " +
            "a.cat_empty_full_id AS catEmptyFullId, " +
            "Tlocal.nombre AS local, " +
            "A.eir_id AS eirId, " +
            "A.fecha_ingreso_camion AS fechaIngresoCamion, " +
            "A.fecha_salida_camion AS fechaSalidaCamion, " +
            "cnt.numero_contenedor AS contenedor, " +
            "cnt.cat_tamano_id AS catTamanoId, " +
            "cnt.cat_tipo_contenedor_id AS catTipoContenedorId, " +
            ":isContainer AS isContainer, " +
            "cnt.cat_clase_id AS catClaseId, " +
            "cnt.codigo_iso_id AS codigoIsoId, " +
            "cnt.linea_naviera_id AS shippingLine, " +
            "NULL AS chassisOwnerCompanyId, " +
            "a.fecha_registro AS fechaRegistro, " +
            "ISNULL(precinto_1,'') + IIF(ISNULL(precinto_2,'')<>'',', ','')+ISNULL(precinto_2,'') + " +
            "IIF(ISNULL(precinto_3,'')<>'',', ','')+ISNULL(precinto_3,'') + IIF(ISNULL(precinto_4,'')<>'',', ','')+ISNULL(precinto_4,'') AS seals, " +
            "a.observacion AS observacion, " +
            "null AS catCargoDocumentTypeId, " +
            "null AS cargoDocument, " +
            "null AS shipperNro, " +
            "null AS shipperName, " +
            "null AS consigneeNro, " +
            "null AS consigneeName, " +
            "null AS operationType, " +
            "null AS productoId, " +
            "A.eir_chassis_id AS eirChassisId, " +
            "sdg.fn_GetEquipmentConditionID(a.eir_id,:isContainer,'S','CUR') AS catStructureConditionId, " +
            "sdg.fn_GetEquipmentConditionID(a.eir_id,:isContainer,'M','CUR') AS catMachineryConditionId, " +
            "a.flag_chassis_stayed AS flagChassisStayed, " +
            "0 AS filter, " +
            "null AS inspectorComment, " +
            "0 AS potencialFoodAid, " +
            "a.observacion AS ginComment, " +
            "0 AS usdaApproved, " +
            "sdg.fn_GetEquipmentConditionID(a.eir_id,:isContainer,'S','INS') AS catStructureConditionInspId, " +
            "sdg.fn_GetEquipmentConditionID(a.eir_id,:isContainer,'M','INS') AS catMachineryConditionInspId, " +
            "NULL AS bookingPreAllocation " +
            "FROM sde.stock_vacio AS stocke (NOLOCK) " +
            "INNER JOIN sde.eir AS A (NOLOCK) ON stocke.eir_ingreso_id = A.eir_id " +
            "INNER JOIN sds.contenedor AS cnt (NOLOCK) ON A.contenedor_id = cnt.contenedor_id " +
            "INNER JOIN seg.unidad_negocio AS Tlocal (NOLOCK) ON a.sub_unidad_negocio_local_id = Tlocal.unidad_negocio_id " +
            "INNER JOIN sds.programacion_nave_detalle AS prnade (NOLOCK) ON a.programacion_nave_detalle_id = prnade.programacion_nave_detalle_id " +
            "INNER JOIN sds.programacion_nave AS prna (NOLOCK) ON prnade.programacion_nave_id = prna.programacion_nave_id " +
            "WHERE A.sub_unidad_negocio_id = :subBusinessUnitId " +
            "AND A.cat_empty_full_id = IIF(:emptyFullId IS NULL, A.cat_empty_full_id, :emptyFullId) " +
            "AND cnt.cat_tamano_id = IIF(:containerSizeId IS NULL, cnt.cat_tamano_id, :containerSizeId) " +
            "AND cnt.cat_tipo_contenedor_id = IIF(:containerTypeId IS NULL, cnt.cat_tipo_contenedor_id, :containerTypeId) " +
            "AND ISNULL(cnt.cat_clase_id, 0) = IIF(:containerGradeId IS NULL, ISNULL(cnt.cat_clase_id, 0), :containerGradeId) " +
            "AND A.linea_naviera_id = IIF(:containerShippingLineId IS NULL, A.linea_naviera_id, :containerShippingLineId) " +
            "AND NOT a.contenedor_id in (:equipmentPendingId, :equipmentNotApplicableId) " +
            "AND NOT A.fecha_salida_camion IS NULL " +
            "AND stocke.en_stock = 1", nativeQuery = true)
    List<TbDataEmpty> getTbDataList(@Param("subBusinessUnitId") Integer subBusinessUnitId,
                                    @Param("emptyFullId") Integer emptyFullId,
                                    @Param("containerSizeId") Integer containerSizeId,
                                    @Param("containerTypeId") Integer containerTypeId,
                                    @Param("containerGradeId") Integer containerGradeId,
                                    @Param("containerShippingLineId") Integer containerShippingLineId,
                                    @Param("equipmentPendingId") Integer equipmentPendingId,
                                    @Param("equipmentNotApplicableId") Integer equipmentNotApplicableId,
                                    @Param("isContainer") Integer isContainer);


    @Query(value = """
        SELECT NULL AS id,
               a.unidad_negocio_id AS businessUnitId,
               a.sub_unidad_negocio_id AS subBusinessUnitId,
               a.programacion_nave_detalle_id AS programmingShipDetailId,
               a.contenedor_id AS containerId,
               NULL AS chassisId,
               a.cat_empty_full_id AS catEmptyFullId,
               Tlocal.nombre AS local,
               a.eir_id AS eirId,
               a.fecha_ingreso_camion AS truckEntryDate,
               a.fecha_salida_camion AS truckExitDate,
               cnt.numero_contenedor AS equipmentNumber,
               cnt.cat_tamano_id AS equipmentSizeId,
               cnt.cat_tipo_contenedor_id AS equipmentTypeId,
               :isContainer AS equipmentCategory,
               cnt.cat_clase_id AS equipmentClassId,
               cnt.codigo_iso_id AS isoCode,
               cnt.linea_naviera_id AS shippingLineId,
               NULL AS chassisOwnerCompanyId,
               a.fecha_registro AS registrationDate,
               ISNULL(precinto_1, '') +
                   IIF(ISNULL(precinto_2, '') <> '', ', ', '') + ISNULL(precinto_2, '') +
                   IIF(ISNULL(precinto_3, '') <> '', ', ', '') + ISNULL(precinto_3, '') +
                   IIF(ISNULL(precinto_4, '') <> '', ', ', '') + ISNULL(precinto_4, '') AS seals,
               a.observacion AS observation,
               NULL AS cargoDocumentTypeId,
               NULL AS cargoDocument,
               NULL AS shipperNumber,
               NULL AS shipperName,
               NULL AS consigneeNumber,
               NULL AS consigneeName,
               NULL AS operationType,
               NULL AS productId,
               a.eir_chassis_id AS eirChassisId,
               sdg.fn_GetEquipmentConditionID(a.eir_id, :isContainer, 'S', 'CUR') AS structureConditionId,
               sdg.fn_GetEquipmentConditionID(a.eir_id, :isContainer, 'M', 'CUR') AS machineryConditionId,
               a.flag_chassis_stayed AS chassisStayedFlag,
               0 AS filter,
               NULL AS inspectorComment,
               0 AS potentialFoodAid,
               a.observacion AS ginComment,
               0 AS usdaApproved,
               sdg.fn_GetEquipmentConditionID(a.eir_id, :isContainer, 'S', 'INS') AS structureConditionInspectionId,
               sdg.fn_GetEquipmentConditionID(a.eir_id, :isContainer, 'M', 'INS') AS machineryConditionInspectionId,
               NULL AS bookingPreAllocation,
               NULL AS inspectionStatus,
               NULL AS estimateStructureNumber,
               NULL AS estimateStructureStatus,
               NULL AS estimateMachineNumber,
               NULL AS estimateMachineStatus,
               a.flag_no_maersk AS noMaerskFlag,
               cnt.fecha_fabricacion AS manufactureDateContainer
        FROM sde.stock_vacio AS stocke
        INNER JOIN sde.eir AS a ON stocke.eir_ingreso_id = a.eir_id
        INNER JOIN sds.contenedor AS cnt ON a.contenedor_id = cnt.contenedor_id
        INNER JOIN seg.unidad_negocio AS Tlocal ON a.sub_unidad_negocio_local_id = Tlocal.unidad_negocio_id
        INNER JOIN sds.programacion_nave_detalle AS prnade ON a.programacion_nave_detalle_id = prnade.programacion_nave_detalle_id
        INNER JOIN sds.programacion_nave AS prna ON prnade.programacion_nave_id = prna.programacion_nave_id
        WHERE a.sub_unidad_negocio_id = :subBusinessUnitId
          AND a.cat_empty_full_id = IIF(:emptyFullId IS NULL, a.cat_empty_full_id, :emptyFullId)
          AND cnt.cat_tamano_id = IIF(:containerSizeId IS NULL, cnt.cat_tamano_id, :containerSizeId)
          AND cnt.cat_tipo_contenedor_id = IIF(:containerTypeId IS NULL, cnt.cat_tipo_contenedor_id, :containerTypeId)
          AND COALESCE(cnt.cat_clase_id, 0) = IIF(:containerGradeId IS NULL, COALESCE(cnt.cat_clase_id, 0), :containerGradeId)
          AND a.linea_naviera_id = IIF(:shippingLineId IS NULL, a.linea_naviera_id, :shippingLineId)
          AND COALESCE(a.flag_no_maersk, 0) = IIF(:nonMaerskFlag IS NULL, COALESCE(a.flag_no_maersk, 0), :nonMaerskFlag)
          AND cnt.numero_contenedor LIKE CONCAT('%', LTRIM(RTRIM(ISNULL(:equipmentNumber, ''))), '%')
          AND NOT a.contenedor_id IN (:equipmentPendingId, :equipmentNotApplicableId)
          AND a.fecha_salida_camion IS NOT NULL
          AND stocke.en_stock = 1
    """, nativeQuery = true)
    List<Object[]> fetchEmptyContainerStockData(
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("emptyFullId") Integer emptyFullId,
            @Param("containerSizeId") Integer containerSizeId,
            @Param("containerTypeId") Integer containerTypeId,
            @Param("containerGradeId") Integer containerGradeId,
            @Param("shippingLineId") Integer shippingLineId,
            @Param("nonMaerskFlag") String nonMaerskFlag,
            @Param("equipmentNumber") String equipmentNumber,
            @Param("equipmentPendingId") Integer equipmentPendingId,
            @Param("equipmentNotApplicableId") Integer equipmentNotApplicableId,
            @Param("isContainer") Integer isContainer
    );

    @Query("""
        SELECT CASE WHEN COUNT(1) > 0 THEN TRUE ELSE FALSE END FROM StockEmpty s
        WHERE s.container = :container
          AND s.inStock = true
          AND s.active = true
        """)
    boolean isContainerInStock(@Param("container") Container container);

    @Query("SELECT s FROM StockEmpty s "
            + "WHERE s.container.id = :containerId "
            + "  AND s.subBusinessUnit.id = :subUnitId "
            + "  AND s.inStock = true "
            + "  AND s.active = true")
    StockEmpty findStockInYard(@Param("containerId") Integer containerId,
                               @Param("subUnitId") Long subUnitId);

    @Query("SELECT se FROM StockEmpty se JOIN se.gateInEir e ON se.gateInEir.id = e.id " +
            "JOIN Container c ON se.container.id = c.id " +
            "where se.active = true and se.inStock = true and e.active = true " +
            "and e.catEmptyFull.id = 43083 " +
            "AND c.forSale = false AND se.subBusinessUnit.id = :subUniId " +
            "AND c.containerNumber LIKE CONCAT(:containerNum, '%') " +
            "ORDER BY c.containerNumber ASC")
    Page<StockEmpty> findStockInventory(Integer subUniId, String containerNum, Pageable pageable);

    @Query("""
    SELECT se FROM StockEmpty se
    WHERE se.gateInEir.id = :eirId
      AND se.active = true
      AND se.gateInEir.active = true
""")
    Optional<StockEmpty> findActiveByGateInEirId(@Param("eirId") Integer eirId);

    @Query("""
            SELECT stk FROM StockEmpty stk
            WHERE stk.gateInEir.container.id = :containerId
            AND stk.gateInEir.subBusinessUnit.id = :subBusinessUnitId            
            AND stk.inStock = :inStock
            AND stk.active = :active
            AND stk.gateInEir.active = :active
            """)
    Optional<StockEmpty> findByContainerAndBusinessUnitAndInStockAndActive(Integer containerId, Integer subBusinessUnitId, Boolean inStock, Boolean active);

    @Query("""
            SELECT TRUE FROM StockEmpty stk
            WHERE stk.gateInEir.container.id = :containerId
            AND stk.subBusinessUnit.id = :subBusinessUnitId
            AND stk.inStock = :inStock
            AND stk.active = :active
            """)
    Boolean existsByContainerIdAndSubBusinessUnitIdAndInStockAndActive(Integer containerId, Integer subBusinessUnitId, boolean inStock, boolean active);

    @Query("""
            SELECT TRUE FROM StockEmpty se
            WHERE se.gateInEir.id = :eirId
              AND se.active = true
              AND se.inStock = true
            """)
    boolean isInStockWithEir(Integer eirId);
}