package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "eir_send_appeir", schema = "sde")
public class EirSendAppeir {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "eir_send_appeir_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "eir_id", nullable = false)
    private Eir eir;

    @NotNull
    @Column(name = "flag_send", nullable = false)
    private Character flagSend;

    @Column(name = "result_message")
    private String resultMessage;

    @NotNull
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

    @Column(name = "status_code")
    private Integer statusCode;

    @NotNull
    @Column(name = "depot_credential_appeir_id", nullable = false)
    private Integer depotCredentialAppeirId;

    @NotNull
    @Column(name = "sub_business_unit_id", nullable = false)
    private Integer subBusinessUnitId;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @Size(max = 200)
    @Column(name = "comment", length = 200)
    private String comment;

    @Size(max = 50)
    @Column(name = "appeir_trace", length = 50)
    private String appeirTrace;

    @Size(max = 1)
    @Column(name = "scope_inspection", length = 1)
    private String scopeInspection;

    @Column(name = "is_damaged_container")
    private Boolean isDamagedContainer;

    @Column(name = "estimated_estructure_id")
    private Integer estimatedEstructureId;

    @Column(name = "estimated_machinery_id")
    private Integer estimatedMachineryId;

    @Column(name = "is_new_insert")
    private Boolean isNewInsert;

    @Column(name = "eir_reference_appeir")
    private Integer eirReferenceAppeir;

    @Column(name = "activity_date")
    private LocalDateTime activityDate;

}