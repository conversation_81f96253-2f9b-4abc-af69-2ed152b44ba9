package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.WorkQueue;
import com.maersk.sd1.common.model.Yard;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface WorkQueueRepository extends JpaRepository<WorkQueue, Integer> {
    @Query("SELECT wq FROM WorkQueue wq WHERE wq.active = true AND wq.yard = :yardId AND wq.byDefect = true")
    List<WorkQueue> findTopByYardIdAndActiveAndByDefect(@Param("yardId") Yard yardId);

    @Query("""
            SELECT wq FROM WorkQueue wq
            WHERE wq.yard.id = :yardId
            AND wq.byDefect = true
            AND wq.active = true
            """)
    Optional<WorkQueue> findDefaultByYardId(Integer yardId);
}