package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.dto.ContainerRestrictionDetailDTO;
import com.maersk.sd1.common.model.ContainerRestriction;
import com.maersk.sd1.sde.dto.TbRestrictionsProjection;
import com.maersk.sd1.sdg.dto.TbRestriction;
import jakarta.persistence.Tuple;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

public interface ContainerRestrictionRepository extends JpaRepository<ContainerRestriction, Integer> {

    @Query("SELECT new com.maersk.sd1.common.dto.ContainerRestrictionDetailDTO(rc.id, rc.restrictionAnnotation, mot.description) " +
            "FROM ContainerRestriction rc " +
            "JOIN ContainerRestrictionDetail rcd ON rc.id = rcd.containerRestriction.id " +
            "JOIN Catalog mot ON rcd.catRestrictionReason.id = mot.id " +
            "WHERE rc.container.id = :containerId " +
            "AND rc.subBusinessUnit.id = :subBusinessUnitId " +
            "AND rc.releasedRestriction = false " +
            "AND rc.catEmptyFull.id = :catEmptyFullId " +
            "AND rc.active = true " +
            "AND rcd.active = true")
    List<ContainerRestrictionDetailDTO> findRestrictions(@Param("containerId") Integer containerId,
                                                         @Param("subBusinessUnitId") Integer subBusinessUnitId,
                                                         @Param("catEmptyFullId") Integer catEmptyFullId);

    @Query(value = """
            SELECT 1 cr
            FROM
            ContainerRestriction cr
            WHERE
            cr.container.id = :equipmentId
            AND cr.subBusinessUnit.id = :subBusinessUnitId
            AND cr.releasedRestriction = :releasedRestriction
            AND cr.catEmptyFull.id = :catEmptyFullId
            AND cr.active = :active
            """)
    ContainerRestriction getContainerRestrictionByEquipmentIdSubBusinessUnitIdReleasedRestrictionCatEmptyFullIdAndActive(@Param("equipmentId") Integer equipmentId, @Param("subBusinessUnitId") Integer subBusinessUnitId,@Param("releasedRestriction") Boolean releasedRestriction, @Param("catEmptyFullId") Integer  catEmptyFullId, @Param("active") Boolean active);



    @Query(value = "SELECT t2.cat_empty_full_id AS catEmptyFullId, " +
            "t2.contenedor_id AS containerId, " +
            "ISNULL(t2.anotacion_restriccion,'') AS restrictionRemark, " +
            "ISNULL((SELECT STRING_AGG(TRIM(motresx.descripcion), ', ') WITHIN GROUP (ORDER BY motresx.descripcion ASC) " +
            "FROM sde.restriccion_contenedor_detalle AS restdet (NOLOCK) " +
            "INNER JOIN ges.catalogo AS motresx (NOLOCK) ON restdet.cat_motivo_restriccion_id = motresx.catalogo_id " +
            "WHERE restdet.restriccion_cnt_id = t2.restriccion_cnt_id and restdet.activo = 1), '') AS numeroBooking " +
            "FROM sde.restriccion_contenedor AS t2 (NOLOCK) " +
            "WHERE t2.restriccion_liberada = 0 AND t2.activo = 1", nativeQuery = true)
    List<TbRestriction> getRestriccionContenedores();


    @Query("SELECT r " +
            "FROM ContainerRestriction r " +
            "WHERE r.container.id = :containerId " +
            "AND r.subBusinessUnit.id = :subBusinessUnitId " +
            "AND r.catEmptyFull.id = :catEmptyFullId " +
            "AND r.releasedRestriction = false " +
            "AND r.active = true")
    ContainerRestriction findContainerRestrictionByParameters(
            @Param("containerId") Integer containerId,
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("catEmptyFullId") Integer catEmptyFullId
    );

    @Query(value = """
          SELECT t2.cat_empty_full_id AS cat_empty_full_id,
                 t2.contenedor_id AS container_id,
                 ISNULL(t2.anotacion_restriccion, '') AS restrictionRemark,
                 LTRIM(STUFF((SELECT ', ' + RTRIM(motresx.descripcion)
                              FROM sde.restriccion_contenedor_detalle AS restdet (NOLOCK)
                              INNER JOIN ges.catalogo AS motresx (NOLOCK)
                              ON restdet.cat_motivo_restriccion_id = motresx.catalogo_id
                              WHERE restdet.restriccion_cnt_id = t2.restriccion_cnt_id
                              AND restdet.activo = 1
                              ORDER BY motresx.descripcion ASC
                              FOR XML PATH('')), 1, 1, '')) AS reasons
          FROM sde.restriccion_contenedor AS t2 (NOLOCK)
          WHERE t2.cat_empty_full_id IN :catEmptyFullIds
          AND t2.restriccion_liberada = 0
          AND t2.activo = 1
          AND t2.contenedor_id IN :containerIds
          AND t2.sub_unidad_negocio_id IN :subBusinessUnitIds
          """, nativeQuery = true)
    List<Object[]> findRestrictionsByContainerIdsSubBusinessUnitIdsAndCatEmptyFullIds(
            @Param("containerIds") Set<Integer> containerIds,
            @Param("subBusinessUnitIds") Set<Integer> subBusinessUnitIds,
            @Param("catEmptyFullIds") Set<Integer> catEmptyFullIds);

    @Query("SELECT r FROM ContainerRestriction r " +
            "WHERE r.catEmptyFull.id IN :catEmptyFullIds " +
            "AND r.container.id IN :containerIds " +
            "AND r.subBusinessUnit.id IN :subBusinessUnitIds " +
            "AND r.releasedRestriction = false " +
            "AND r.active = true")
    List<ContainerRestriction> findActiveRestrictions(@Param("catEmptyFullIds") List<Integer> catEmptyFullIds,
                                                      @Param("containerIds") List<Integer> containerIds,
                                                      @Param("subBusinessUnitIds") List<Integer> subBusinessUnitIds);


    @Query("SELECT cr FROM ContainerRestriction cr "
            + "WHERE cr.active = true "
            + "  AND cr.releasedRestriction = false "
            + "  AND cr.catEmptyFull.id = :emptyFullId "
            + "  AND cr.subBusinessUnit.id = :subBusinessUnitId "
            + "  AND cr.container.id = :containerId ")
    List<ContainerRestriction> findActiveRestriction(@Param("containerId") Integer containerId,
                                                     @Param("subBusinessUnitId") Long subBusinessUnitId,
                                                     @Param("emptyFullId") Integer emptyFullId);

    List<ContainerRestriction> findByContainerIdAndSubBusinessUnitIdAndCatEmptyFullIdAndReleasedRestrictionAndActive(
            Integer containerId,
            Integer subBusinessUnitId,
            Integer catEmptyFullId,
            Boolean releasedRestriction,
            Boolean active
    );

    @Query("SELECT cr FROM ContainerRestriction cr " +
            "WHERE cr.container.id = :containerId " +
            "  AND cr.subBusinessUnit.id = :subBusinessUnitId " +
            "  AND cr.catEmptyFull.id = :isFullCatalogId " +
            "  AND cr.releasedRestriction = false " +
            "  AND cr.active = true")
    List<ContainerRestriction> findActiveFullRestrictions(@Param("containerId") Integer containerId,
                                                          @Param("subBusinessUnitId") Integer subBusinessUnitId,
                                                          @Param("isFullCatalogId") Integer isFullCatalogId);

    @Query("SELECT cr FROM ContainerRestriction cr " +
            "WHERE cr.subBusinessUnit.id = :subBusinessUnitId " +
            "  AND cr.container.id = :containerId " +
            "  AND cr.releasedRestriction = false " +
            "  AND cr.active = true " +
            "  AND cr.catEmptyFull.id = :catEmptyFullId")
    List<ContainerRestriction> findActiveRestrictionsForContainer(@Param("subBusinessUnitId") Long subBusinessUnitId,
                                                                  @Param("containerId") Integer containerId,
                                                                  @Param("catEmptyFullId") Integer catEmptyFullId);
    @Query("SELECT r FROM ContainerRestriction r " +
            "WHERE r.container.id = :containerId " +
            "AND r.subBusinessUnit.id = :subUniId " +
            "AND r.releasedRestriction = false " +
            "AND r.active = true")
    List<ContainerRestriction> findActiveRestrictionsOfContainer(@Param("containerId") Integer containerId,
                                                      @Param("subUniId") Integer subUniId);

    @Query("SELECT STRING_AGG(d.catRestrictionReason.description, ', ') " +
            "FROM ContainerRestrictionDetail d " +
            "WHERE d.containerRestriction.id = :restrictionId ")
    String findRestrictionMotives(@Param("restrictionId") Integer restrictionId);

    @Query(value = """
            SELECT t2.contenedor_id AS containerId,
                   '[' + FORMAT(t2.fecha_restriccion, :formatDate) + ' | ' + ISNULL(t2.anotacion_restriccion, '') + ': ' +
                   LTRIM(STUFF((SELECT ', ' + RTRIM(motresx.descripcion)
                                FROM sde.restriccion_contenedor_detalle AS restdet (NOLOCK)
                                INNER JOIN ges.catalogo AS motresx (NOLOCK) ON restdet.cat_motivo_restriccion_id = motresx.catalogo_id
                                WHERE restdet.restriccion_cnt_id = t2.restriccion_cnt_id AND restdet.activo = 1
                                ORDER BY motresx.descripcion ASC FOR XML PATH('')), 1, 1, '')) + ']' AS restrictionDetails
            FROM sde.restriccion_contenedor AS t2 (NOLOCK)
            WHERE t2.contenedor_id = :containerId
            AND t2.sub_unidad_negocio_id = :subBusinessUnitId
            AND t2.cat_empty_full_id = 43083
            AND t2.restriccion_liberada = 0
            AND t2.activo = 1
            ORDER BY t2.fecha_restriccion DESC
            """, nativeQuery = true)
    List<TbRestrictionsProjection> findRestrictionsByContainerId(@Param("containerId") Integer containerId,
                                                                 @Param("subBusinessUnitId") Integer subBusinessUnitId,
                                                                 @Param("formatDate") String formatDate);

    @Query(value = """
        SELECT
            RC.restriccion_cnt_id AS restrictionId,
            CNT.numero_contenedor AS containerNumber,
            CEF.catalogo_id AS restrictionTypeId,
            CEF.descricion_larga AS restrictionType,
            RC.restriccion_liberada AS restrictionStatus,
            TMN.catalogo_id AS terminalContainerId,
            TMN.descripcion AS terminalContainer,
            TP.catalogo_id AS containerTypeId,
            TP.descripcion AS containerType,
            CNT.linea_naviera_id AS shippingLineId,
            LN.nombre AS shippingLine,
            (
                SELECT JSON_QUERY((
                    SELECT RCD.restriccion_cnt_detalle_id,
                           RCD.activo,
                           CMR.catalogo_id,
                           CMR.descripcion
                    FROM sde.restriccion_contenedor_detalle RCD (NOLOCK)
                    INNER JOIN ges.catalogo CMR (NOLOCK) ON RCD.cat_motivo_restriccion_id = CMR.catalogo_id
                    WHERE RCD.restriccion_cnt_id = RC.restriccion_cnt_id
                    FOR JSON AUTO
                ))
            ) AS restrictionReasons,
            '' AS booking,
            RC.fecha_restriccion AS restrictionDate,
            USR.usuario_id AS restrictionUserId,
            USR.nombres AS restrictionUserFirstName,
            (ISNULL(USR.apellido_paterno, '') + ' ' + ISNULL(USR.apellido_paterno, '')) AS restrictionUserLastName,
            CML.catalogo_id AS releaseReasonId,
            [sds].[fn_CatalogoTraducidoDes](CML.catalogo_id, :languageId) AS releaseReason,
            RC.fecha_libera_restriccion AS releaseDate,
            USL.usuario_id AS releaseUserId,
            USL.nombres AS releaseUserFirstName,
            (ISNULL(USL.apellido_paterno, '') + ' ' + ISNULL(USL.apellido_paterno, '')) AS releaseUserLastName,
            COR.catalogo_id AS restrictionOriginId,
            COR.descripcion AS restrictionOrigin,
            COL.catalogo_id AS releaseOriginId,
            COL.descripcion AS releaseOrigin
        FROM sde.restriccion_contenedor RC
            LEFT JOIN ges.catalogo CEF ON RC.cat_empty_full_id = CEF.catalogo_id
            LEFT JOIN ges.catalogo CML ON RC.cat_motivo_libera_restriccion_id = CML.catalogo_id
            LEFT JOIN ges.catalogo COR ON RC.cat_origen_restriccion_id = COR.catalogo_id
            LEFT JOIN ges.catalogo COL ON RC.cat_origen_libera_restriccion_id = COL.catalogo_id
            LEFT JOIN seg.usuario USR ON RC.usuario_restriccion_id = USR.usuario_id
            LEFT JOIN seg.usuario USL ON RC.usuario_libera_restriccion_id = USL.usuario_id
            LEFT JOIN sds.contenedor CNT ON RC.contenedor_id = CNT.contenedor_id
            LEFT JOIN ges.catalogo TP ON CNT.cat_tipo_contenedor_id = TP.catalogo_id
            LEFT JOIN ges.catalogo TMN ON CNT.cat_tamano_id = TMN.catalogo_id
            LEFT JOIN sds.linea_naviera LN ON CNT.linea_naviera_id = LN.linea_naviera_id
        WHERE
            (:restrictionId IS NULL OR RC.restriccion_cnt_id = :restrictionId)
            AND ((:restrictionStartDate IS NULL AND :restrictionEndDate IS NULL)
                OR RC.fecha_restriccion BETWEEN :restrictionStartDate AND :restrictionEndDate)
            AND (:containers IS NULL OR CNT.numero_contenedor IN (
                SELECT numero_contenedor
                FROM OPENJSON(:containers)
                WITH (numero_contenedor VARCHAR(100) '$.value')
            ))
            AND RC.sub_unidad_negocio_id = :subBusinessUnitId
            AND RC.ACTIVO = 1
            ORDER BY RC.restriccion_cnt_id DESC
        """, nativeQuery = true)
    List<Tuple> findContainerRestrictions(
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("restrictionId") Integer restrictionId,
            @Param("restrictionStartDate") LocalDateTime restrictionStartDate,
            @Param("restrictionEndDate") LocalDateTime restrictionEndDate,
            @Param("containers") String containers,
            @Param("languageId") Integer languageId
    );
}