package com.maersk.sd1.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class SystemProjectId implements Serializable {
    private static final long serialVersionUID = -8475984463103903311L;
    @NotNull
    @Column(name = "sistema_id", nullable = false)
    private Integer systemId;

    @NotNull
    @Column(name = "menu_id", nullable = false)
    private Integer menuId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        SystemProjectId entity = (SystemProjectId) o;
        return Objects.equals(this.systemId, entity.systemId) &&
                Objects.equals(this.menuId, entity.menuId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(systemId, menuId);
    }

}