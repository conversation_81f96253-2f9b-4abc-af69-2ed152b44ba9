package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class BusinessUnitObtenerInput {

    @Data
    public static class Input {
        @JsonProperty("unidad_negocio_id")
        @NotNull(message = "unidad_negocio_id cannot be null")
        private Integer businessUnitId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("ADM")
        private Prefix prefix;
    }
}

