package com.maersk.sd1.seg.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ServicioListDTO {
    private Integer servicioId;
    private String servicioNombre;
    private Character indicadorProtegido;
    private Boolean estado;
    private Integer usuarioRegistroId;
    private LocalDateTime fechaRegistro;
    private Integer usuarioModificacionId;
    private LocalDateTime fechaModificacion;
    private String usuarioNombres;
    private String usuarioApellidos;

    public ServicioListDTO(
            Integer servicioId,
            String servicioNombre,
            Character indicadorProtegido,
            Boolean estado,
            Integer usuarioRegistroId,
            LocalDateTime fechaRegistro,
            Integer usuarioModificacionId,
            LocalDateTime fechaModificacion,
            String usuarioNombres,
            String usuarioApellidos
    ) {
        this.servicioId = servicioId;
        this.servicioNombre = servicioNombre;
        this.indicadorProtegido = indicadorProtegido;
        this.estado = estado;
        this.usuarioRegistroId = usuarioRegistroId;
        this.fechaRegistro = fechaRegistro;
        this.usuarioModificacionId = usuarioModificacionId;
        this.fechaModificacion = fechaModificacion;
        this.usuarioNombres = usuarioNombres;
        this.usuarioApellidos = usuarioApellidos;
    }
}

