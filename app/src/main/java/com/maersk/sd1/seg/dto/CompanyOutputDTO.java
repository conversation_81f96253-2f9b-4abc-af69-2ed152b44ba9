package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CompanyOutputDTO {
    @JsonProperty("documento")
    private String documento;

    @JsonProperty("tipo_documento_descripcion")
    private String tipoDocumentoDescripcion;

    @JsonProperty("razon_social")
    private String razonSocial;

    @JsonProperty("unidad_negocio_nombre")
    private String unidadNegocioNombre;

    @JsonProperty("estado")
    private Boolean estado;

    public CompanyOutputDTO() {
    }

    public CompanyOutputDTO(String documento, String tipoDocumentoDescripcion, String razonSocial, String unidadNegocioNombre, Boolean estado) {
        this.documento = documento;
        this.tipoDocumentoDescripcion = tipoDocumentoDescripcion;
        this.razonSocial = razonSocial;
        this.unidadNegocioNombre = unidadNegocioNombre;
        this.estado = estado;
    }
}

