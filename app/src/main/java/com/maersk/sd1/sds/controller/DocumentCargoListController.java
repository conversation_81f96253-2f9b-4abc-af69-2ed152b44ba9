package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.DocumentCargoListInput;
import com.maersk.sd1.sds.dto.DocumentCargoListOutput;
import com.maersk.sd1.sds.service.DocumentCargoListService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSDocumentoCargaServiceImp")
public class DocumentCargoListController {

    private static final Logger logger = LogManager.getLogger(DocumentCargoListController.class);

    private final DocumentCargoListService documentCargoService;

    @Autowired
    public DocumentCargoListController(DocumentCargoListService documentCargoService) {
        this.documentCargoService = documentCargoService;
    }

    @PostMapping("/sdsdocumentCargoList")
    public ResponseEntity<ResponseController<DocumentCargoListOutput>> documentCargoList(@RequestBody @Valid DocumentCargoListInput.Root request) {
        try {
            if (request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                return ResponseEntity.badRequest().build();
            }

            DocumentCargoListInput.Input input = request.getPrefix().getInput();
            DocumentCargoListOutput result = documentCargoService.searchDocumentCargo(input);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            logger.error("An error occurred while fetching cargo documents.", e);
            DocumentCargoListOutput errorOutput = new DocumentCargoListOutput();
            errorOutput.setRespTotal(List.of(List.of(0)));
            errorOutput.setRespDocuments(null);
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}