package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.PortRepository;
import com.maersk.sd1.sds.dto.PortListInput;
import com.maersk.sd1.sds.dto.PortListItemDTO;
import com.maersk.sd1.sds.dto.PortListOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Service
public class PortListService {

    private static final Logger logger = LogManager.getLogger(PortListService.class);

    private final PortRepository portRepository;

    @Autowired
    public PortListService(PortRepository portRepository) {
        this.portRepository = portRepository;
    }

    @Transactional(readOnly = true)
    public PortListOutput getPortList(PortListInput.Input input) {
        PortListOutput output = new PortListOutput();
        try {
            int pageNumber = (input.getPage() == null || input.getPage() < 1) ? 0 : input.getPage() - 1;
            int size = (input.getSize() == null || input.getSize() < 1) ? Integer.MAX_VALUE : input.getSize();

            LocalDate registrationDateMin = input.getRegistrationDateMin();
            LocalDate registrationDateMax = input.getRegistrationDateMax();
            LocalDate modificationDateMin = input.getModificationDateMin();
            LocalDate modificationDateMax = input.getModificationDateMax();

            LocalDateTime registrationDateMinTime = registrationDateMin != null
                    ? registrationDateMin.atStartOfDay()
                    : null;
            LocalDateTime registrationDateMaxTime = registrationDateMax != null
                    ? registrationDateMax.atTime(LocalTime.MAX)
                    : null;
            LocalDateTime modificationDateMinTime = modificationDateMin != null
                    ? modificationDateMin.atStartOfDay()
                    : null;
            LocalDateTime modificationDateMaxTime = modificationDateMax != null
                    ? modificationDateMax.atTime(LocalTime.MAX)
                    : null;

            Page<PortListItemDTO> pageResult = portRepository.findAllByFilters(
                    input.getPortId(),
                    input.getPort(),
                    input.getName(),
                    input.getCountryName(),
                    input.getActive(),
                    registrationDateMinTime,
                    registrationDateMaxTime,
                    modificationDateMinTime,
                    modificationDateMaxTime,
                    PageRequest.of(pageNumber, size)
            );

            long totalElements = pageResult.getTotalElements();

            List<PortListItemDTO> finalList = pageResult.stream().map(item -> {
                item.setRegistrationDateLocal("");
                item.setModificationDateLocal("");
                return item;
            }).toList();

            output.setTotalRecords(List.of(List.of(totalElements)));
            output.setPorts(finalList);
        } catch (Exception e) {
            logger.error("Error fetching port list", e);
            output.setTotalRecords(List.of(List.of(0L)));
            output.setPorts(null);
        }
        return output;
    }
}