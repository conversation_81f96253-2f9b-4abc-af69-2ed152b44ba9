package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.VesselProgrammingPortDeleteInput;
import com.maersk.sd1.sds.dto.VesselProgrammingPortDeleteOutput;
import com.maersk.sd1.sds.service.VesselProgrammingPortDeleteService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSProgramacionNaveServiceImp")
public class VesselProgrammingPortDeleteController {

    private static final Logger logger = LogManager.getLogger(VesselProgrammingPortDeleteController.class);

    private final VesselProgrammingPortDeleteService vesselProgrammingPortDeleteService;

    @PostMapping("/sdseliminarProgramacionNavePuerto")
    public ResponseEntity<ResponseController<VesselProgrammingPortDeleteOutput>> deleteVesselProgrammingPort(
            @RequestBody @Valid VesselProgrammingPortDeleteInput.Root request) {
        try {

            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                return ResponseEntity.badRequest().body(new ResponseController<>(new VesselProgrammingPortDeleteOutput()));
            }

            logger.info("Request received to delete VesselProgrammingPort: {}", request);

            VesselProgrammingPortDeleteInput.Input input = request.getPrefix().getInput();
            VesselProgrammingPortDeleteOutput output = vesselProgrammingPortDeleteService.deleteVesselProgrammingPort(
                    input.getVesselProgrammingPortId(),
                    input.getUserRegistrationId(),
                    input.getLanguageId()
            );

            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the deleteVesselProgrammingPort request.", e);
            VesselProgrammingPortDeleteOutput output = new VesselProgrammingPortDeleteOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.internalServerError().body(new ResponseController<>(output));
        }
    }
}
