package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.EirDeleteInput;
import com.maersk.sd1.sds.controller.dto.EirDeleteOutput;
import com.maersk.sd1.sds.service.EirDeleteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.net.URISyntaxException;

@RestController
@RequestMapping("module/sde/SDEEIRServiceImp")
public class EirDeleteController {

    private final EirDeleteService eirDeleteService;

    @Autowired
    public EirDeleteController(EirDeleteService eirDeleteService) {
        this.eirDeleteService = eirDeleteService;
    }

    @PostMapping("/sdseirEliminar")
    public ResponseEntity<ResponseController<EirDeleteOutput>> isoCodeListService(@RequestBody EirDeleteInput.Root input) throws IOException {

        try {
            return ResponseEntity.ok(new ResponseController<>(eirDeleteService.eirDeleteProcess(input.getPrefix().getInput())));
        } catch (IOException | URISyntaxException e) {
            throw new IOException(e);
        }
    }
}
