package com.maersk.sd1.sdh.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Output DTO following the sample style with respEstado, respMensaje, and an optional jsonError.
 */
@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY )
public class DocumentChassisRegisterGateOutOutput {

    @JsonProperty("resp_result")
    private Integer respEstado;

    @JsonProperty("resp_message")
    private String respMensaje;

    @JsonProperty("JSON_ERROR")
    private String jsonError;
}

