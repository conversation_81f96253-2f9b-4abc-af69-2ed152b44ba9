package com.maersk.sd1.sdh.service;

import com.maersk.sd1.sdh.dto.ChassisInspectionInputDTO;
import com.maersk.sd1.sdh.dto.ChassisInspectionOutputDTO;

import jakarta.persistence.EntityManager;
import jakarta.persistence.ParameterMode;
import jakarta.persistence.StoredProcedureQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.System;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Service class to handle chassis inspection logic.
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class ChassisInspectionService {

    private final EntityManager entityManager;

    private static final String INSPECTION_ALIAS = "sd1_chassis_zoneactiv_insp"; // equals 48848

    @Transactional(readOnly = true)
    public ChassisInspectionOutputDTO getChassisInspectionData(ChassisInspectionInputDTO.Input input) {
        ChassisInspectionOutputDTO output = new ChassisInspectionOutputDTO();
            Integer eirChassisZoneActivityId = input.getEirChassisZoneActivityId();
            Long userId = input.getUserId();
            Integer languageId = input.getLanguageId();

            StoredProcedureQuery query = entityManager.createStoredProcedureQuery("[sdh].[inspection_chassis_get_data]");

            query.registerStoredProcedureParameter("eir_chassis_zone_activity_id", Integer.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("user_id", Long.class, ParameterMode.IN);
            query.registerStoredProcedureParameter("language_id", Integer.class, ParameterMode.IN);

            query.setParameter("eir_chassis_zone_activity_id", eirChassisZoneActivityId);
            query.setParameter("user_id", userId);
            query.setParameter("language_id", languageId);

            query.execute();

            // List to store all result sets, including empty ones
            List<List<Object[]>> allResults = new ArrayList<>();

            boolean hasMoreResults = true;
            while (hasMoreResults) {
                List<Object[]> resultSet = query.getResultList(); // Get current result set
                allResults.add(resultSet); // Always add the list, even if empty

                hasMoreResults = query.hasMoreResults(); // Check if another result set exists
            }

            for (int i = 0; i < allResults.size(); i++) {
                System.out.println("Result Set " + (i + 1) + ": " + (allResults.get(i).isEmpty() ? "Empty" : ""));
                for (Object[] row : allResults.get(i)) {
                    System.out.println(Arrays.toString(row));
                }
            }

        return mapChassisInspectionData(allResults);

    }

    public ChassisInspectionOutputDTO mapChassisInspectionData(List<List<Object[]>> allResults) {
        ChassisInspectionOutputDTO outputDTO = new ChassisInspectionOutputDTO();

        List<ChassisInspectionOutputDTO.ResultState> statusList = new ArrayList<>();
        List<ChassisInspectionOutputDTO.ChassisInspectionHeaderDTO> headerList = new ArrayList<>();
        List<ChassisInspectionOutputDTO.NextZoneDTO> nextZoneListWithoutDamage = new ArrayList<>();
        List<ChassisInspectionOutputDTO.NextZoneDTO> nextZoneListWithDamage = new ArrayList<>();
        List<ChassisInspectionOutputDTO.ChassisInspectionPartialDetailDTO> partialDetailsList = new ArrayList<>();
        List<ChassisInspectionOutputDTO.ChassisInspectionPhotoDTO> headerPhotosList = new ArrayList<>();
        List<ChassisInspectionOutputDTO.ChassisInspectionDetailPhotoDTO> detailPhotosList = new ArrayList<>();
        List<ChassisInspectionOutputDTO.EirChassisObservationDTO> observationsList = new ArrayList<>();

        for (int i = 0; i < allResults.size(); i++) {
            List<Object[]> resultSet = allResults.get(i);
            if (!resultSet.isEmpty()) {
                Object[] firstRow = resultSet.get(0);
                if (firstRow.length == 2) {
                    // Status
                    for (Object[] row : resultSet) {
                        ChassisInspectionOutputDTO.ResultState status = new ChassisInspectionOutputDTO.ResultState();
                        status.setResultState((Character) row[0]);
                        status.setResultMessage((String) row[1]);
                        statusList.add(status);
                    }
                } else if (firstRow.length == 12) {
                    // Inspection Header
                    for (Object[] row : resultSet) {
                        ChassisInspectionOutputDTO.ChassisInspectionHeaderDTO header = new ChassisInspectionOutputDTO.ChassisInspectionHeaderDTO();
                        header.setEirChassisId((Integer) row[0]);
                        header.setTruckInDate(row[1].toString());
                        header.setReferenceNumber((String) row[2]);
                        header.setChassisNumber((String) row[3]);
                        header.setCatChassisTypeId(row[4] != null ? ((BigDecimal) row[4]).intValue() : null);
                        header.setCatTareUnitId(row[5] != null ? ((BigDecimal) row[5]).intValue() : null);
                        header.setTare(row[6]!= null ? ((BigDecimal) row[6]).intValue() : null);
                        header.setOwnerCompanyId(row[7]!= null ? ((BigDecimal) row[7]).intValue() : null);
                        header.setOwnerCompanyName((String) row[8]);
                        header.setIsPartialInspection((Boolean) row[9]);
                        header.setInspectorComment((String) row[10]);
                        header.setInspectionObservationComment((String) row[11]);
                        headerList.add(header);
                    }
                } else if (i==2) {
                    // Next Zone WithDamage
                    for (Object[] row : resultSet) {
                            ChassisInspectionOutputDTO.NextZoneDTO nextZone = new ChassisInspectionOutputDTO.NextZoneDTO();
                            nextZone.setGetNextZoneId(((BigDecimal) row[0]).intValue());
                            nextZone.setGetNextZoneAlias((String) row[1]);
                            nextZone.setGetNextZoneAltId((Integer) row[2]);
                            nextZone.setGetNextZoneAltAlias((String) row[3]);
                            nextZone.setComment((String) row[4]);
                            nextZone.setCommentAlt((String) row[5]);
                            nextZoneListWithoutDamage.add(nextZone);
                    }
                }
                else if (i==3) {
                    // Next Zone Without Damage
                    for (Object[] row : resultSet) {
                        ChassisInspectionOutputDTO.NextZoneDTO nextZone = new ChassisInspectionOutputDTO.NextZoneDTO();
                        nextZone.setGetNextZoneId(((BigDecimal) row[0]).intValue());
                        nextZone.setGetNextZoneAlias((String) row[1]);
                        nextZone.setGetNextZoneAltId((Integer) row[2]);
                        nextZone.setGetNextZoneAltAlias((String) row[3]);
                        nextZone.setComment((String) row[4]);
                        nextZone.setCommentAlt((String) row[5]);
                        nextZoneListWithDamage.add(nextZone);
                    }
                }
                else if (firstRow.length == 31) {
                    // Partial Inspection Details
                    for (Object[] row : resultSet) {
                        ChassisInspectionOutputDTO.ChassisInspectionPartialDetailDTO detail = new ChassisInspectionOutputDTO.ChassisInspectionPartialDetailDTO();
                        detail.setItem(((Long) row[0]).intValue());
                        detail.setChassisEstimateDetailId(((Integer) row[1]));
                        detail.setCatChaestimDamageLocationId(((BigDecimal) row[2]).intValue());
                        detail.setUbicacionDano((String) row[3]);
                        detail.setCatChaestimDamageTypeId(((BigDecimal) row[4]).intValue());
                        detail.setTipoDano((String) row[5]);
                        detail.setCatChaestimMaterialTypeId(((BigDecimal) row[6]).intValue());
                        detail.setTipoMaterial((String) row[7]);
                        detail.setCatChaestimRepairTypeId(((BigDecimal) row[8]).intValue());
                        detail.setTipoReparacion((String) row[9]);
                        detail.setCatChaestimComponentId(((BigDecimal) row[10]).intValue());
                        detail.setComponente((String) row[11]);
                        detail.setCatChaestimResponsibilityId(((BigDecimal) row[12]).intValue());
                        detail.setResponsable((String) row[13]);
                        detail.setCatChaestimSizeRepairId(((BigDecimal) row[14]).intValue());
                        detail.setTipoDimensionDano((String) row[15]);
                        detail.setSizeRepairLength((BigDecimal) row[16]);
                        detail.setSizeRepairWidth((BigDecimal) row[17]);
                        detail.setDimension((String) row[18]);
                        detail.setQuantityRepair(((BigDecimal) row[19]).intValue());
                        detail.setManHourPorPiece((BigDecimal) row[20]);
                        detail.setCostoTrabajoPorPieza((BigDecimal) row[21]);
                        detail.setMaterialCostPerPiece((BigDecimal) row[22]);
                        detail.setTotalCost((BigDecimal) row[23]);
                        detail.setChassisEstimateDetailComment((String) row[24]);
                        detail.setChassisOwnerCompanyId(((BigDecimal) row[25]).intValue());
                        detail.setCatChaestimSizeRepairMeasureUnitId(row[26] != null ? ((BigDecimal) row[26]).intValue() : null);
                        detail.setRepairLocationDesc((String) row[27]);
                        detail.setDamageTypeDesc((String) row[28]);
                        detail.setComponentDesc((String) row[29]);
                        detail.setRepairMethodDesc((String) row[30]);
                        partialDetailsList.add(detail);
                    }
                } else if (firstRow.length == 5) {
                    // Header Photos
                    for (Object[] row : resultSet) {
                        ChassisInspectionOutputDTO.ChassisInspectionPhotoDTO photo = new ChassisInspectionOutputDTO.ChassisInspectionPhotoDTO();
                        photo.setChassisEstimateId((Integer) row[0]);
                        photo.setChassisEstimateEirPhotoId((Integer) row[1]);
                        photo.setAdjuntoId(((BigDecimal) row[2]).intValue());
                        photo.setPhotoStorageId((String) row[3]);
                        photo.setUrl((String) row[4]);
                        headerPhotosList.add(photo);
                    }
                } else if (firstRow.length == 6) {
                    // Detail Photos
                    for (Object[] row : resultSet) {
                        ChassisInspectionOutputDTO.ChassisInspectionDetailPhotoDTO photo = new ChassisInspectionOutputDTO.ChassisInspectionDetailPhotoDTO();
                        photo.setChassisEstimateId((Integer) row[0]);
                        photo.setChassisEstimateDetailId((Integer) row[1]);
                        photo.setChassisEstimateDetailPhotoId((Integer) row[2]);
                        photo.setAdjuntoId((Integer) row[3]);
                        photo.setPhotoStorageId((String) row[4]);
                        photo.setUrl((String) row[5]);
                        detailPhotosList.add(photo);
                    }
                } else if (firstRow.length == 3) {
                    // Inspector Observations
                    for (Object[] row : resultSet) {
                        ChassisInspectionOutputDTO.EirChassisObservationDTO observation = new ChassisInspectionOutputDTO.EirChassisObservationDTO();
                        observation.setCatObservationEirChassisId(((BigDecimal) row[0]).intValue());
                        observation.setRowNumber(((Long) row[1]).intValue());
                        observation.setObservacionDescripcionSecos((String) row[2]);
                        observationsList.add(observation);
                    }
                }
            }
        }

        outputDTO.setStatus(statusList);
        outputDTO.setInspectionHeader(headerList);
        outputDTO.setNextZoneWithdamage(nextZoneListWithDamage);
        outputDTO.setNextZoneWithoutdamage(nextZoneListWithoutDamage);
        outputDTO.setPartialInspectionDetails(partialDetailsList);
        outputDTO.setHeaderPhotos(headerPhotosList);
        outputDTO.setDetailPhotos(detailPhotosList);
        outputDTO.setInspectorObservations(observationsList);

        return outputDTO;
    }
}