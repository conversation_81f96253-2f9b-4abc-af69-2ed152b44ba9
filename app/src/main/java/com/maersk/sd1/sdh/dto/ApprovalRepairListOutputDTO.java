package com.maersk.sd1.sdh.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@JsonFormat (shape = JsonFormat.Shape.ARRAY)
public class ApprovalRepairListOutputDTO {

    @JsonProperty("total_registros")
    private List<List<Integer>> totalRegistros;

    @JsonProperty("approval_repair_list")
    @JsonFormat (shape = JsonFormat.Shape.ARRAY)
    private List<ApprovalRepairItem> approvalRepairList;

    @Data
    public static class ApprovalRepairItem {
        @JsonProperty("chassis_eir")
        private Integer chassisEir;

        @JsonProperty("owner_name")
        private String ownerName;

        @JsonProperty("chassis_number")
        private String chassisNumber;

        @JsonProperty("chassis_type_name")
        private String chassisTypeName;

        @JsonProperty("structure_condition")
        private String structureCondition;

        @JsonProperty("check1_selectable")
        private Boolean check1Selectable;

        @JsonProperty("check1_next_zone")
        private String check1NextZone;

        @JsonProperty("check2_selectable")
        private Boolean check2Selectable;

        @JsonProperty("check2_next_zone")
        private String check2NextZone;

        @JsonProperty("comment")
        private String comment;

        @JsonProperty("approval_date")
        private LocalDateTime approvalDate;

        @JsonProperty("user_approval_id")
        private Long userApprovalId;

        @JsonProperty("user_approval_name")
        private String userApprovalName;

        @JsonProperty("user_approval_lastname")
        private String userApprovalLastname;

        @JsonProperty("eir_date")
        private LocalDateTime eirDate;
    }
}

