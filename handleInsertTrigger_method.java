    /**
     * Handles the insert trigger logic equivalent to the SQL trigger.
     * This method implements the business logic that was previously handled by a database trigger.
     * 
     * @param eirId The EIR ID to process
     */
    public void handleInsertTrigger(Integer eirId) {
        try {
            // Get the EIR record
            Eir eir = eirRepository.findById(eirId).orElse(null);
            if (eir == null) {
                return;
            }

            // Extract variables from the EIR record
            Integer subBusinessUnitId = eir.getSubBusinessUnit().getId();
            Integer catMoveTypeId = eir.getCatMovement().getId();
            Integer shippingLineId = eir.getShippingLine() != null ? eir.getShippingLine().getId() : null;
            LocalDateTime truckArrival = eir.getTruckArrivalDate();
            Integer containerId = eir.getContainer() != null ? eir.getContainer().getId() : null;
            Integer catEmptyFullId = eir.getCatEmptyFull() != null ? eir.getCatEmptyFull().getId() : null;

            // Get catalog IDs for gate_in, gate_out, and empty
            Integer isGateOut = catalogRepository.findIdByAliasName(CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS);
            Integer isGateIn = catalogRepository.findIdByAliasName(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS);
            Integer isEmpty = catalogRepository.findIdByAliasName(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS);

            // Get equipment not applicable ID
            Container equipmentNotApplicable = containerRepository.findByContainerNumber("NOT APPLICA").orElse(null);
            Integer equipmentNotApplicableId = equipmentNotApplicable != null ? equipmentNotApplicable.getId() : null;

            // Check conditions: gate_in movement, empty container, not 'NOT APPLICA', within 20 days
            if (Objects.equals(catMoveTypeId, isGateIn) 
                    && Objects.equals(catEmptyFullId, isEmpty) 
                    && containerId != null 
                    && !Objects.equals(containerId, equipmentNotApplicableId)
                    && truckArrival != null
                    && ChronoUnit.DAYS.between(truckArrival.toLocalDate(), LocalDate.now()) <= 20) {

                // Find depot credential appeir configuration
                List<DepotCredentialAppeir> depotCredentials = depotCredentialAppeirRepository.findAll()
                        .stream()
                        .filter(dc -> Objects.equals(dc.getSubBusinessUnit().getId(), subBusinessUnitId)
                                && Objects.equals(dc.getShippingLineId(), shippingLineId)
                                && Boolean.TRUE.equals(dc.getSendGateIn())
                                && Boolean.TRUE.equals(dc.getActive()))
                        .toList();

                if (!depotCredentials.isEmpty()) {
                    DepotCredentialAppeir depotCredential = depotCredentials.get(0);
                    
                    // Create and save EirSendAppeir record
                    EirSendAppeir eirSendAppeir = EirSendAppeir.builder()
                            .depotCredentialAppeirId(depotCredential.getId())
                            .subBusinessUnitId(subBusinessUnitId)
                            .eir(eir)
                            .flagSend('0')
                            .registrationDate(LocalDateTime.now())
                            .active(true)
                            .isNewInsert(false)
                            .appeirTrace("new0")
                            .build();
                    
                    eirSendAppeirRepository.save(eirSendAppeir);
                }
            }
        } catch (Exception e) {
            // Log the error but don't throw to avoid breaking the main flow
            System.err.println("Error in handleInsertTrigger: " + e.getMessage());
            e.printStackTrace();
        }
    }
